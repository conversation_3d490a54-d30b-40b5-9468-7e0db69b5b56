{"battleparam_alienCopRestoreHP": 30, "battleparam_austinBattleLoot": "100 10 20 25", "battleparam_banditTruckAttackCooldown": 20, "battleparam_banditTruckMissionId": 321, "battleparam_billManualShootTapRadius": 60, "battleparam_bonusZombieRage": 15, "battleparam_bonusZombieRageChance": 10, "battleparam_builderRunSelfDamage": 20, "battleparam_builderRunSpecialSpeed": 20, "battleparam_carolJumpMinDistance": 50, "battleparam_cephalopodaFinalStrikeCooldown": 60, "battleparam_cephalopodaFireRepeatDamage": 10, "battleparam_cephalopodaSimpleAttackCooldown": 20, "battleparam_cephalopodaSimpleAttackDamage": 150, "battleparam_cephalopodaUltDamage": 9999, "battleparam_chopperHealCooldown": 60, "battleparam_chopperPercentForHeal": 0.5, "battleparam_demonBonusDmg": 2, "battleparam_eggChargeCooldown": 5, "battleparam_eliteHumanChance": 10, "battleparam_eventGrenadeCooldown": 3, "battleparam_eventGrenadeDamage": 39, "battleparam_friendlyFireOn": false, "battleparam_generatorOnSpawnDamage": 7, "battleparam_grenaderGrenadeCooldownSeconds": 10, "battleparam_grenaderGrenadeDamage": 160, "battleparam_humanStartGroup": "h_naked h_naked h_naked h_naked 4", "battleparam_immortalTimeAfterResurrectSeconds": 0.5, "battleparam_insectJumpColliderSize": "45 40 20", "battleparam_insectJumpDamage": 10, "battleparam_largeMissionZombieSpawnCooldown": 10, "battleparam_medkitHealCount": 10, "battleparam_medkitHealDelaySec": 1.5, "battleparam_moraleOn": true, "battleparam_noiseCooldownSec": 3, "battleparam_noiseReduceSpeed": 25, "battleparam_onDeadConvertion_0": "z_skeletonSapper z_skeletonMillitary 1", "battleparam_onDeadConvertion_1": "z_skeletonSapper None 1", "battleparam_onDeadConvertion_2": "z_skeletonMillitary z_blackSkeleton 1", "battleparam_onDeadConvertion_3": "z_skeletonMillitary None 1", "battleparam_onDeadConvertion_4": "b_crazy z_nakedRun 1", "battleparam_onDeadConvertion_5": "h_heavyGuard h_heavyGuard2 1", "battleparam_onDeadConvertion_List": "0 1 2 3 4", "battleparam_psyMaxPushDistance": 200, "battleparam_psyPushForce": 0.5, "battleparam_psyPushSpeed": 185, "battleparam_pvpLeftTeam": "h_builder h_farmer h_naked h_farmer h_naked h_naked ", "battleparam_PVPReplicsCooldown": 15, "battleparam_pvpRightTeam": "h_farmer h_farmer h_naked h_farmer h_naked h_farmer h_builder ", "battleparam_pvpWaterGainSpeedPer10Seconds": 30, "battleparam_queenGrenadeCooldownSeconds": 0, "battleparam_randomAggression": true, "battleparam_randomAgility": true, "battleparam_randomCritChance": true, "battleparam_randomMoveSpeed": true, "battleparam_randomPushResist": 0.1, "battleparam_randomSpawnZombies": "z_freehugs 1 70 30", "battleparam_ShieldRadius": 100, "battleparam_ShieldSize": 30, "battleparam_spawnAfterDeathOn": true, "battleparam_startingCooldownSkipSeconds": 10, "battleparam_startingManaResource": 10, "battleparam_startingRageResource": 2, "battleparam_stopUnitUponReachingStopPosition": 0, "battleparam_swatFireChance": 16, "battleparam_swatFireDamage": 3, "battleparam_TimeToAutoHideWater": 10, "battleparam_turretOnReloadDamage": 10, "battleparam_turretRechargeTime": 3, "battleparam_unitFreezeDebuffTime": 0.5, "battleparam_waterBuffDropAmount": 10, "battleparam_waterGainSpeedPer10Seconds": 15, "battleparam_zombieResurrectHPPercent": 100, "battleparam_zombieSpawnDensity": "100 100", "buff_EnergyDrink": {"InGameDuration": "16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16,16", "Value": "-50,-50,-50,-50,-50,-50,-50,-50,-50,-50,-50,-50,-50,-50,-50,-50,-50,-50,-50,-50"}, "buff_EnhancedTeam": {"InGameDuration": "25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25", "Value": "100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100,100"}, "buff_ImprovedGenerator": {"InGameDuration": "25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25", "Value": "10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10"}, "BusUpgrade_1": {"damage": 13, "durability": 100, "speed": 110, "SlotAdded": "Paint1 Tube1 Step1 Wheel1 TopPipe1", "upgradeCost": 0}, "BusUpgrade_10": {"damage": 31, "durability": 200, "speed": 200, "upgradeCost": 1264}, "BusUpgrade_11": {"damage": 34, "durability": 200, "speed": 210, "SlotAdded": "Shield6", "upgradeCost": 1747}, "BusUpgrade_12": {"damage": 37, "durability": 210, "speed": 220, "upgradeCost": 2461}, "BusUpgrade_13": {"damage": 41, "durability": 220, "speed": 230, "SlotAdded": "Shield7", "upgradeCost": 3518}, "BusUpgrade_14": {"damage": 45, "durability": 230, "speed": 240, "upgradeCost": 5087}, "BusUpgrade_15": {"damage": 49, "durability": 240, "speed": 250, "SlotAdded": "Shield8", "upgradeCost": 7418}, "BusUpgrade_16": {"damage": 54, "durability": 250, "speed": 260, "upgradeCost": 10884}, "BusUpgrade_17": {"damage": 60, "durability": 260, "speed": 270, "upgradeCost": 16040}, "BusUpgrade_18": {"damage": 66, "durability": 270, "speed": 280, "upgradeCost": 23716}, "BusUpgrade_19": {"damage": 72, "durability": 280, "speed": 290, "upgradeCost": 35149}, "BusUpgrade_2": {"damage": 14, "durability": 110, "speed": 120, "SlotAdded": "Shield1", "upgradeCost": 38}, "BusUpgrade_3": {"damage": 16, "durability": 120, "speed": 130, "SlotAdded": "Shield2", "upgradeCost": 108}, "BusUpgrade_4": {"damage": 17, "durability": 130, "speed": 140, "upgradeCost": 186}, "BusUpgrade_5": {"damage": 19, "durability": 140, "SlotAdded": "Shield3", "speed": 150, "upgradeCost": 228}, "BusUpgrade_6": {"damage": 21, "durability": 150, "speed": 160, "upgradeCost": 457}, "BusUpgrade_7": {"damage": 23, "durability": 160, "SlotAdded": "Shield4", "speed": 170, "upgradeCost": 562}, "BusUpgrade_8": {"damage": 25, "durability": 170, "speed": 180, "upgradeCost": 714}, "BusUpgrade_9": {"damage": 28, "durability": 180, "speed": 190, "SlotAdded": "Shield5", "upgradeCost": 936}, "carpart_BusWheels1": {"buyPrice": 50, "chanceDropExplore": 0, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_BusWheels2": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_BusWheels3": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_BusWheels4": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_BusWheels5": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_BusWheels6": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_Pipe1": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_Pipe2": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_Pipe3": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_Pipe4": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_Step1": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_TopPipe1": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_TopPipe2": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_Window1": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_Window2": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_Window3": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "carpart_Window4": {"buyPrice": 50, "chanceDropExplore": 1, "durability": 2, "sellPrice": 150, "shield": 0, "type": "RareItem"}, "discount_bundle_1": "Starter", "discount_bundle_1_percent": 20, "discount_bundle_1_tier": 1, "discount_bundle_2": "Heist", "discount_bundle_2_percent": 30, "discount_bundle_2_tier": 2, "discount_bundle_3": "Spec<PERSON>", "discount_bundle_3_percent": 50, "discount_bundle_3_tier": 2, "discount_bundle_4": "Swat", "discount_bundle_4_percent": 70, "discount_bundle_4_tier": 3, "discount_bundle_5": "Circus", "discount_bundle_5_percent": 85, "discount_bundle_5_tier": 3, "discount_bundle_6": "StarterSale", "discount_bundle_6_percent": 60, "discount_bundle_6_tier": 0, "discount_bundle_7": "Agent", "discount_bundle_7_percent": 20, "discount_bundle_7_tier": 1, "discount_bundle_cooldown": "16:00:00", "discount_bundle_duration": "1.00:00:00", "discount_essence_10_cooldownOnComplete": "3.00:00:00", "discount_essence_10_cooldownOnComplete@dev": "00:01:00", "discount_essence_10_duration": "1.00:00:00", "discount_essence_10_duration@dev": "0.02:03:00", "discount_essence_10_element_1_discount": 75, "discount_essence_10_element_1_packName": "ValuePack2", "discount_essence_10_element_1_type": "ValuePack", "discount_essence_10_element_1_weight": 1, "discount_essence_10_weight": 1, "discount_essence_11_cooldownOnComplete": "3.00:00:00", "discount_essence_11_cooldownOnComplete@dev": "00:01:00", "discount_essence_11_duration": "1.00:00:00", "discount_essence_11_duration@dev": "0.02:03:00", "discount_essence_11_element_1_discount": 50, "discount_essence_11_element_1_packName": "ValuePack3", "discount_essence_11_element_1_type": "ValuePack", "discount_essence_11_element_1_weight": 1, "discount_essence_11_weight": 1, "discount_essence_12_cooldownOnComplete": "3.00:00:00", "discount_essence_12_cooldownOnComplete@dev": "00:01:00", "discount_essence_12_duration": "1.00:00:00", "discount_essence_12_duration@dev": "0.02:03:00", "discount_essence_12_element_1_discount": 50, "discount_essence_12_element_1_packName": "ValuePack4", "discount_essence_12_element_1_type": "ValuePack", "discount_essence_12_element_1_weight": 1, "discount_essence_12_weight": 1, "discount_essence_9_cooldownOnComplete": "3.00:00:00", "discount_essence_9_cooldownOnComplete@dev": "00:01:00", "discount_essence_9_duration": "1.00:00:00", "discount_essence_9_duration@dev": "0.02:03:00", "discount_essence_9_element_1_discount": 75, "discount_essence_9_element_1_packName": "ValuePack1", "discount_essence_9_element_1_type": "ValuePack", "discount_essence_9_element_1_weight": 1, "discount_essence_9_weight": 1, "discount_lastChanceConvertion": 6, "discount_shopBundles": "1 2 3 4 5 7", "discount_valuePackSequence": "9 10 11 12", "iap_legboxes1": {"BoxCount": 1, "Discount": 0}, "iap_legboxes2": {"BoxCount": 4, "Discount": -25}, "iap_legboxes3": {"BoxCount": 10, "Discount": -33}, "iap_legboxes4": {"BoxCount": 25, "Discount": -40}, "item_blood": {"bulletDamage": 0, "buyPrice": 200, "chanceDropExplore": 2, "cost": 0, "damage": 0, "fortune": 0, "healthPoints": 3, "moveSpeed": 0, "sellPrice": 600, "type": "LegendaryItem"}, "item_bread": {"bulletDamage": 0, "buyPrice": 50, "chanceDropExplore": 70, "cost": 0, "damage": 0, "fortune": 0, "healthPoints": 1, "moveSpeed": 0, "sellPrice": 150, "type": "CommonItem"}, "item_brush": {"bulletDamage": 1, "buyPrice": 50, "chanceDropExplore": 70, "cost": 0, "damage": 0, "fortune": 0, "healthPoints": 0, "moveSpeed": 0, "sellPrice": 150, "type": "CommonItem"}, "item_chocolate": {"bulletDamage": 0, "buyPrice": 100, "chanceDropExplore": 28, "cost": 0, "damage": 0, "fortune": 2, "healthPoints": 0, "moveSpeed": 0, "sellPrice": 300, "type": "RareItem"}, "item_drug": {"bulletDamage": 0, "buyPrice": 100, "chanceDropExplore": 28, "cost": 0, "damage": 2, "fortune": 0, "healthPoints": 0, "moveSpeed": 0, "sellPrice": 300, "type": "RareItem"}, "item_fizzy": {"bulletDamage": 0, "buyPrice": 200, "chanceDropExplore": 2, "cost": 0, "damage": 0, "fortune": 3, "healthPoints": 0, "moveSpeed": 0, "sellPrice": 600, "type": "LegendaryItem"}, "item_flank": {"bulletDamage": 0, "buyPrice": 200, "chanceDropExplore": 2, "cost": 0, "damage": 3, "fortune": 0, "healthPoints": 0, "moveSpeed": 0, "sellPrice": 600, "type": "LegendaryItem"}, "item_glue": {"bulletDamage": 3, "buyPrice": 200, "chanceDropExplore": 2, "cost": 0, "damage": 0, "fortune": 0, "healthPoints": 0, "moveSpeed": 0, "sellPrice": 600, "type": "LegendaryItem"}, "item_powder": {"bulletDamage": 2, "buyPrice": 100, "chanceDropExplore": 28, "cost": 0, "damage": 0, "fortune": 0, "healthPoints": 0, "moveSpeed": 0, "sellPrice": 300, "type": "RareItem"}, "item_radio": {"bulletDamage": 0, "buyPrice": 50, "chanceDropExplore": 70, "cost": 0, "damage": 0, "fortune": 1, "healthPoints": 0, "moveSpeed": 0, "sellPrice": 150, "type": "CommonItem"}, "item_stew": {"bulletDamage": 0, "buyPrice": 100, "chanceDropExplore": 28, "cost": 0, "damage": 0, "fortune": 0, "healthPoints": 2, "moveSpeed": 0, "sellPrice": 300, "type": "RareItem"}, "item_tablets": {"bulletDamage": 0, "buyPrice": 50, "chanceDropExplore": 70, "cost": 0, "damage": 1, "fortune": 0, "healthPoints": 0, "moveSpeed": 0, "sellPrice": 150, "type": "CommonItem"}, "itemUpg_item_barrel": {"Damage": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "HitPoints": "30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49"}, "itemUpg_item_bomb": {"Explosive": "75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94"}, "itemUpg_item_generator": {"HitPoints": "105,107,109,111,113,115,117,119,121,123,125,127,129,131,133,135,137,139,141,143", "LifeTime": "50.5,51,51.5,52,52.5,53,53.5,54,54.5,55,55.5,56,56.5,57,57.5,58,58.5,59,59.5,60", "SpawnSpeed": "20,19,18,17,16,15,15,14,14,14,13,13,13,12,12,12,11,11,11,10"}, "itemUpg_item_medkit": {"Healing": "3,5,7,9,11,13,15,17,19,21,23,25,27,29,31,33,35,37,39,41"}, "itemUpg_item_molotov": {"Damage": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "FireDamage": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24"}, "itemUpg_item_nitrogen": {"Damage": "18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37", "Duration": "5,5.5,6.0,6.5,7.0,7.5,8.0,8.5,9.0,9.5,10.0,10.5,11.0,11.5,12.0,12.5,13.0,13.5,14.0,14.5", "HitPoints": "30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49"}, "itemUpg_item_red_barrel": {"Damage": "18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37", "Explosive": "75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94", "FireDamage": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24", "HitPoints": "48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67"}, "itemUpg_item_turret": {"Damage": "3,5,7,9,11,13,15,17,19,21,23,25,27,29,31,33,35,37,39,41", "FireDuration": "60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98", "HitPoints": "21,23,25,27,29,31,33,35,37,39,41,43,45,47,49,51,53,55,57,59", "LifeTime": "25,25.5,26.0,26.5,27.0,27.5,28.0,28.5,29.0,29.5,30.0,30.5,31.0,31.5,32.0,32.5,33.0,33.5,34.0,34.5"}, "KTPlayManager_Deeplink_SetDiscussion_1": "KTRL.1508418974", "KTPlayManager_Deeplink_SetDiscussion_2": "KTRL.1508419107", "KTPlayManager_Deeplink_SetDiscussion_3": "KTRL.1508419143", "KTPlayManager_Deeplink_SetDiscussion_4": "KTRL.1508419159", "KTPlayManager_Deeplink_SetDiscussion_5": "KTRL.1508419177", "KTPlayManager_Deeplink_SetDiscussion_6": "KTRL.1508419202", "KTPlayManager_Deeplink_SetDiscussion_7": "KTRL.1508419241", "KTPlayManager_Deeplink_SetDiscussion_8": "KTRL.1530102942", "mission_1": {"backgroundName": "set1_welcome", "cameraOverlayColor": "127 148 127 255", "coins": "9 3", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_welcome", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_naked 8", "mainWaveRepeats": 22, "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_naked", "rewardItems": "item_bread 100", "set": 1, "startWave": "z_naked", "water": "40 25", "xp": "27 1", "zombieBaseHp": 300, "zombieSpawnDensity": "100 100"}, "mission_102": {"backgroundName": "set1_forest", "cameraOverlayColor": "127 148 127 255", "coins": "9 3", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_forest", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "4 z_naked 15 z_naked 12", "mainWaveRepeats": 12, "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_naked", "rewardItems": "item_tablets 100", "set": 1, "startWave": "z_naked z_naked z_naked", "water": "40 25", "xp": "27 1", "zombieBaseHp": 200, "zombieSpawnDensity": "100 100"}, "mission_103": {"backgroundName": "set1_warehouse", "cameraOverlayColor": "127 148 127 255", "coins": "9 3", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_warehouse", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_naked 4 z_naked 38", "mainWaveRepeats": 8, "missionComplexity": 0, "missionObjects": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 155, "Z": 121}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 149, "Z": 106}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 130, "Z": 127}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 40, "Z": 84}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": -26, "Z": 114}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_naked", "rewardItems": "item_radio 100", "set": 1, "startWave": "z_naked z_naked z_naked z_naked z_naked z_naked", "water": "40 25", "xp": "27 1", "zombieBaseHp": 200, "zombieSpawnDensity": "100 100"}, "mission_104": {"backgroundName": "set1_road", "cameraOverlayColor": "127 148 127 255", "coins": "11 4", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_road", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_naked 8 z_naked z_naked 9 z_naked 11", "mainWaveRepeats": 18, "missionComplexity": 0, "missionObjects": [{"Name": "RoadSign", "X": 58, "Z": 109}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_naked", "set": 1, "startWave": "z_naked", "water": "40 25", "xp": "32 2", "zombieBaseHp": 300, "zombieSpawnDensity": "100 100"}, "mission_105": {"backgroundName": "set1_forest", "cameraOverlayColor": "140 140 100 255", "coins": "11 4", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_forest", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_naked 7 z_nakedRun 9 z_nakedRun 21 z_nakedRun ", "mainWaveRepeats": 10, "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_naked", "set": 1, "startWave": "z_naked", "water": "40 25", "xp": "32 2", "zombieBaseHp": 300, "zombieSpawnDensity": "100 100"}, "mission_106": {"backgroundName": "set1_road", "cameraOverlayColor": "140 140 100 255", "coins": "11 4", "extraReward": "GrayTruck1(item_drug 1)", "extraWave": "GrayTruck1(58 180; z_naked)", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_road", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_naked z_naked 5 z_nakedRun 9 z_nakedRun 9 z_nakedRun 5 z_nakedRun 9 z_nakedRun 9 z_naked 5 z_nakedRun 9 z_nakedRun 15", "mainWaveRepeats": 12, "missionComplexity": 0, "missionObjects": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 130, "Z": 108}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 162, "Z": 104}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 172, "Z": 50}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 165, "Z": 18}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_naked", "rewardItems": "item_powder 33", "set": 1, "startWave": "z_naked z_naked", "water": "40 25", "xp": "32 2", "zombieBaseHp": 300, "zombieSpawnDensity": "100 100"}, "mission_107": {"backgroundName": "set1_street", "cameraOverlayColor": "140 140 100 255", "coins": "13 4", "extraReward": "CUV(item_tablets 1; item_brush 1)", "extraWave": "CUV(-148 182; z_puke)", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_street", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_naked 11 z_nakedRun 1 z_nakedRun 16 z_naked  z_naked z_naked 11 z_nakedRun 1 z_nakedRun 16 z_naked  z_naked 10 z_naked z_naked 11 z_nakedRun 1 z_nakedRun 16 z_naked  z_naked 30", "mainWaveRepeats": 9, "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 1, "startWave": "z_naked z_naked z_naked z_naked z_naked z_naked z_naked z_naked", "water": "40 25", "xp": "37 3", "zombieBaseHp": 300, "zombieSpawnDensity": "100 100"}, "mission_108": {"backgroundName": "set1_church", "cameraOverlayColor": "148 141 96 255", "coins": "13 4", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_church", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_skeleton z_skeleton z_skeleton 10 z_skeleton z_skeleton 10 z_skeleton 8 z_skeleton  z_skeleton  z_skeleton 5", "mainWaveRepeats": 8, "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_radio 20", "set": 1, "spawnUnderTheRoad": "z_skeleton 3", "startWave": "z_skeleton", "water": "40 25", "xp": "37 3", "zombieBaseHp": 300, "zombieSpawnDensity": "100 100"}, "mission_109": {"backgroundName": "set1_street", "cameraOverlayColor": "148 141 96 255", "coins": "13 4", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_street", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_puke z_naked z_naked z_naked 10 z_naked z_puke 7 z_nakedRun 7 z_naked 5 z_naked z_puke", "mainWaveRepeats": 8, "missionComplexity": 0, "missionObjects": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 150, "Z": -18}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 164, "Z": 6}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 150, "Z": 104}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 156, "Z": 30}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 1, "startWave": "z_puke", "water": "40 25", "xp": "37 3", "zombieBaseHp": 300, "zombieSpawnDensity": "100 100"}, "mission_110": {"backgroundName": "set1_street", "cameraOverlayColor": "148 141 96 255", "coins": "13 4", "extraReward": " CUV1(item_radio 1)", "extraWave": " CUV1(-48 182);", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_street", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "8 z_naked z_puke 5 z_puke 8 z_nakedRun z_puke z_puke z_puke 5 z_puke z_puke z_skeleton z_skeleton 8 z_naked z_puke 5 z_puke 8 z_nakedRun z_puke z_puke z_puke 5 z_puke z_puke 1 z_skeleton z_skeleton 3", "mainWaveRepeats": 8, "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_chocolate 100", "set": 1, "startWave": "z_naked z_naked z_naked z_naked z_naked", "water": "40 25", "xp": "37 3", "zombieBaseHp": 300, "zombieSpawnDensity": "100 100"}, "mission_111": {"backgroundName": "set1_cafe", "cameraOverlayColor": "155 139 75 255", "coins": "15 5", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_cafe", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "7 z_puke 1 z_puke 4 z_naked 1 z_puke 6 z_nakedRun 1 z_puke 1 z_puke 6 z_puke 1 z_puke 1 z_puke 1 z_puke 1", "mainWaveRepeats": 10, "missionComplexity": 0, "missionObjects": [{"Name": "Radio", "X": 78, "Z": 100}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_tablets 60 item_bread 30 item_chocolate 20", "set": 1, "startWave": "z_puke z_puke z_naked z_puke z_naked z_naked", "water": "40 25", "xp": "43 4", "zombieBaseHp": 400, "zombieSpawnDensity": "100 100"}, "mission_112": {"backgroundName": "set1_church", "cameraOverlayColor": "155 139 75 255", "coins": "15 5", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_church", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_skeleton z_skeleton z_skeleton 10 z_naked z_naked 10 z_nakedRun 8 z_naked  z_naked  z_naked z_nakedRun z_nakedRun ", "mainWaveRepeats": 8, "missionComplexity": 0, "missionObjects": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 14, "Z": 114}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 109, "Z": 70}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 100, "Z": 50}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_bread 30", "set": 1, "spawnUnderTheRoad": "z_skeleton 4", "startWave": "z_skeleton z_skeleton ", "water": "40 25", "xp": "43 4", "zombieBaseHp": 300, "zombieSpawnDensity": "100 100"}, "mission_113": {"backgroundName": "set1_boss", "cameraOverlayColor": "150 135 70 255", "coins": "15 5", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_boss", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_naked 5 z_naked 7", "mainWaveRepeats": 8, "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 1, "startWave": "z_megavolt z_puke z_puke", "water": "40 25", "xp": "43 4", "zombieBaseHp": 300, "zombieSpawnDensity": "100 100"}, "mission_114": {"backgroundName": "set1_welcome", "cameraOverlayColor": "134 115 60 255", "coins": "30 10", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_welcome", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_nakedRun 5 z_nakedRun z_nakedRun z_naked z_naked z_naked z_naked z_puke z_puke z_puke z_puke z_puke z_puke 50 z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun 20", "mainWaveRepeats": 8, "missionComplexity": 2, "missionObjects": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 155, "Z": 94}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_bread 60; item_powder 10", "set": 1, "startWave": "z_puke z_puke z_puke z_naked z_naked z_naked z_naked z_skeleton z_skeleton z_skeleton z_skeleton z_skeleton z_skeleton z_puke z_puke z_puke z_puke z_puke", "unlock_stars": 30, "water": "40 25", "xp": "43 4", "zombieBaseHp": 400, "zombieSpawnDensity": "100 100"}, "mission_115": {"backgroundName": "set1_welcome", "cameraOverlayColor": "134 115 60 255", "coins": "30 10", "extraReward": " CUV1(item_tablets 1)", "extraWave": " CUV1(-34 180);", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_welcome", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_megavolt z_megavolt z_nakedRun 30 z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun 30 z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun 30 z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun 30 z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun 999", "mainWaveRepeats": 8, "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_radio 100; item_drug 20; item_powder 10", "set": 1, "startWave": "z_puke z_puke z_puke z_puke z_puke z_puke z_puke z_puke z_puke z_naked z_naked z_naked z_naked z_puke z_puke z_naked z_naked z_naked z_naked z_skeleton z_skeleton z_skeleton z_skeleton z_skeleton z_skeleton", "unlock_stars": 39, "water": "40 25", "xp": "43 4", "zombieBaseHp": 400, "zombieSpawnDensity": "100 100"}, "mission_116": {"backgroundName": "set1_church2", "cameraOverlayColor": "134 115 60 255", "coins": "30 10", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set1_forest", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_megavolt z_megavolt z_dog 20 z_dog 5 z_dog z_dog 1 z_dog 1 z_dog 1 z_dog 20 z_dog 5 z_dog z_dog 1 z_dog 1 z_dog 1 z_dog 20 z_dog 5 z_dog z_dog 1 z_dog 1 z_dog 1 z_dog 20 z_dog 5 z_dog z_dog 1 z_dog 1 z_dog 1 z_dog 30 z_dog 5 z_dog z_dog 1 z_dog 1 z_dog 1 z_dog 20 z_dog 5 z_dog z_dog 1 z_dog 1 z_dog 1 z_dog 999", "mainWaveRepeats": 8, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_tablets 100; item_stew 10", "set": 1, "spawnUnderTheRoad": "z_skeleton 1", "startWave": "z_puke z_puke z_puke z_puke z_puke z_puke z_puke z_puke z_puke z_naked z_naked z_naked z_naked z_puke z_puke z_naked z_naked z_naked z_naked z_skeleton z_skeleton z_skeleton z_skeleton z_skeleton z_skeleton", "unlock_stars": 45, "water": "40 25", "xp": "43 4", "zombieBaseHp": 400, "zombieSpawnDensity": "100 100"}, "mission_194": {"backgroundName": "set-5", "coins": "0 0", "finalWave": "z_naked", "foregroundName": "set-5", "inBattleReward": "coins 0 10 10", "isAdditional": false, "mainWave": "z_naked", "mainWaveRepeats": 22, "missionComplexity": 3, "missionTime": 30, "noiseLevel": 100000, "noiseWave": "z_naked", "set": -5, "startWave": "z_naked", "water": "40 25", "xp": "0 0", "zombieBaseHp": 30000, "zombieSpawnDensity": "100 100"}, "mission_201": {"backgroundName": "set2_taxi", "cameraOverlayColor": "*********** 255", "coins": "15 5", "extraReward": "Taxi1(item_stew 1; item_bread 1)", "extraWave": "Taxi1(86 190; z_nakedRun)", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_blocks", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_nakedRun z_puke 12 z_naked z_naked 20 z_nakedRun z_nakedRun 20 z_naked z_nakedRun z_puke", "mainWaveRepeats": 10, "missionComplexity": 0, "missionObjects": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 14, "Z": 114}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 109, "Z": 70}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 100, "Z": 50}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 2, "startWave": "z_naked z_puke", "water": "40 25", "xp": "43 4", "zombieBaseHp": 400, "zombieSpawnDensity": "100 100"}, "mission_202": {"backgroundName": "set2_roadworks2", "cameraOverlayColor": "*********** 255", "coins": "15 5", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_roadworks2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "20 z_builder 8 z_puke z_puke z_naked 9 z_builder 9 z_naked z_naked 10 z_puke z_puke z_naked 9 z_builder 9 z_naked z_naked 10 z_builder 8 z_puke z_puke z_naked 10", "mainWaveRepeats": 15, "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_drug 20", "set": 2, "startWave": "z_naked z_builder z_builder z_builder z_puke z_builder z_naked z_naked z_builder", "water": "40 25", "xp": "43 4", "zombieBaseHp": 400, "zombieSpawnDensity": "100 100"}, "mission_203": {"backgroundName": "set2_roadworks", "cameraOverlayColor": "*********** 255", "coins": "15 5", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_roadworks", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "3 z_naked<PERSON>un z_builder 8 z_builder z_builder 9 z_builder 9 ", "mainWaveRepeats": 6, "missionComplexity": 0, "missionObjects": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 116, "Z": 126}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 132, "Z": 114}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 8, "Z": 112}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 174, "Z": 6}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 178, "Z": 26}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 148, "Z": -2}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 2, "startWave": "z_builder z_builder", "water": "40 25", "xp": "43 4", "zombieBaseHp": 400, "zombieSpawnDensity": "100 100"}, "mission_204": {"backgroundName": "set2_policestation", "cameraOverlayColor": "146 144 125 255", "coins": "17 6", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_barrels", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "17 z_hunter  z_hunter 4 z_girl 8 z_puke z_puke 2 z_puke z_puke 30", "mainWaveRepeats": 10, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_bread 20", "set": 2, "startWave": "z_puke z_girl z_girl", "water": "40 25", "weather": "Sandstorm", "xp": "49 5", "zombieBaseHp": 400, "zombieSpawnDensity": "100 100"}, "mission_205": {"backgroundName": "set2_fence", "cameraOverlayColor": "146 144 125 255", "coins": "17 6", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_policecars", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "15  z_dog 3 z_dog z_dog 1 z_dog 1 z_dog 1 z_dog 2 z_puke z_puke 1 z_puke 2 z_puke 14", "mainWaveRepeats": 10, "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 2, "startWave": "z_puke", "water": "40 25", "xp": "49 5", "zombieBaseHp": 500, "zombieSpawnDensity": "100 100"}, "mission_206": {"backgroundName": "set2_policestation", "cameraOverlayColor": "146 144 125 255", "coins": "17 6", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_policecars", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_naked  z_cop 10 z_cop 2 z_cop 10 z_puke 10", "mainWaveRepeats": 7, "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_radio 20", "set": 2, "startWave": "z_cop z_cop", "water": "40 25", "xp": "49 5", "zombieBaseHp": 500, "zombieSpawnDensity": "100 100"}, "mission_207": {"backgroundName": "set2_hospital", "cameraOverlayColor": "146 144 125 255", "coins": "17 6", "extraReward": " Van1(item_drug 1; item_radio 1)", "extraWave": "Van1(-37 196; z_megavolt)", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_roadworks2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_hunter z_cop 7 z_cop 1 z_cop z_megavolt 8 z_paramedic 15 z_hunter z_cop 7 z_cop 1 z_cop 8 z_paramedic z_paramedic 15 z_cop 7 z_cop z_cop 8 z_paramedic 8 ", "mainWaveRepeats": 15, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 2, "startWave": "z_paramedic", "water": "40 25", "weather": "Sandstorm", "xp": "49 5", "zombieBaseHp": 500, "zombieSpawnDensity": "100 100"}, "mission_208": {"backgroundName": "set2_motel", "cameraOverlayColor": "150 138 124 255", "coins": "17 6", "extraReward": " CUV1(item_bread 1; item_radio 1)", "extraWave": "CUV1(76 186; z_copsmall)", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_roadworks2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "15 z_hunter z_hunter z_girl z_chopper z_chopper 1 z_hunter 24", "mainWaveRepeats": 15, "missionComplexity": 2, "missionObjects": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 147, "Z": 106}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 130, "Z": 112}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 111, "Z": 90}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 162, "Z": 10}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 142, "Z": -4}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 2, "startWave": "z_puke z_chopper z_girl z_girl", "water": "40 25", "xp": "49 5", "zombieBaseHp": 500, "zombieSpawnDensity": "100 100"}, "mission_209": {"backgroundName": "set2_church", "cameraOverlayColor": "150 138 124 255", "coins": "17 6", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_church", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "15 z_dog 1 z_dog 3 z_dog z_dog z_dog 5 z_cop z_cop 19", "mainWaveRepeats": 15, "missionComplexity": 0, "missionObjects": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 6, "Z": 123}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": -16, "Z": 134}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": -7, "Z": 113}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 161, "Z": 14}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 140, "Z": 9}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 2, "spawnUnderTheRoad": "z_skeleton 2", "startWave": "z_skeleton z_skeleton z_skeleton z_skeleton z_skeleton z_skeleton z_skeleton z_skeleton z_cop", "water": "40 25", "xp": "49 5", "zombieBaseHp": 500, "zombieSpawnDensity": "100 100"}, "mission_210": {"backgroundName": "set2_church", "cameraOverlayColor": "150 138 124 255", "coins": "17 6", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_church", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "15 z_dog 1 z_dog 2 z_dog z_dog z_dog z_dog z_dog 5 z_skeleton z_skeleton 19 z_dog z_dog z_dog ", "mainWaveRepeats": 3, "missionComplexity": 1, "missionObjects": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 145, "Z": 127}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 121, "Z": 132}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 150, "Z": 26}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 126, "Z": 120}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_drug 20", "set": 2, "spawnUnderTheRoad": "z_skeleton 1", "startWave": "z_skeleton z_skeleton z_skeleton", "water": "40 25", "xp": "49 5", "zombieBaseHp": 500, "zombieSpawnDensity": "100 100"}, "mission_211": {"backgroundName": "set2_policestation", "cameraOverlayColor": "150 138 124 255", "coins": "20 7", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_policecars", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "17 z_cop z_cop z_dog 6 z_dog z_dog 2 z_dog 2 z_dog 2 z_dog 8 z_dog 4 z_dog z_dog 2 z_dog 2 z_dog 2 z_dog", "mainWaveRepeats": 3, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 2, "startWave": "z_puke z_naked z_puke z_naked z_puke z_naked z_puke z_naked z_cop z_cop z_cop", "water": "40 25", "xp": "57 9", "zombieBaseHp": 600, "zombieSpawnDensity": "100 100"}, "mission_212": {"backgroundName": "set2_roadworks", "cameraOverlayColor": "145 124 105 255", "coins": "20 7", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_roadworks2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_builderRun 5 z_builder 5 z_cop z_builder z_builder z_naked 12 z_builderRun z_builderRun 10 z_naked 10 z_builderRun 10 z_builderRun 1 ", "mainWaveRepeats": 3, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 2, "startWave": "z_builder z_builder", "water": "40 25", "weather": "Sandstorm", "xp": "57 9", "zombieBaseHp": 600, "zombieSpawnDensity": "100 100"}, "mission_213": {"backgroundName": "set2_hospital", "cameraOverlayColor": "145 124 105 255", "coins": "20 7", "extraReward": " Van1(item_tablets 1; item_chocolate 1)", "extraWave": "Van1(-37 196; z_cop)", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_ambulancecar", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_hunter z_cop 7 z_cop z_cop z_megavolt 8 z_paramedic 15 z_hunter z_cop 7 z_cop z_cop 8 z_paramedic 15 z_hunter z_cop 8 z_cop z_cop 9 z_paramedic 9 ", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 2, "startWave": "z_paramedic z_paramedic", "water": "40 25", "xp": "57 9", "zombieBaseHp": 600, "zombieSpawnDensity": "100 100"}, "mission_214": {"backgroundName": "set2_policestation", "cameraOverlayColor": "145 124 105 255", "coins": "20 7", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_policecars", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "14 z_cop 1 z_cop 10 z_cop 1 z_cop ", "mainWaveRepeats": 3, "missionComplexity": 0, "missionObjects": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 155, "Z": 94}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 68, "Z": 106}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_powder 20", "set": 2, "startWave": "z_cop ", "water": "40 25", "xp": "57 9", "zombieBaseHp": 600, "zombieSpawnDensity": "100 100"}, "mission_215": {"backgroundName": "set2_dump", "cameraOverlayColor": "145 124 105 255", "coins": "20 7", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_barrels", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_boomer z_builder z_builder 4 z_naked  z_boomer z_naked z_puke z_puke z_builder z_builder z_builder 15", "mainWaveRepeats": 3, "missionComplexity": 1, "missionObjects": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 123, "Z": 112}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 104, "Z": 120}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 18, "Z": 107}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 150, "Z": 14}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 2, "startWave": "z_boomer ", "water": "40 25", "xp": "57 9", "zombieBaseHp": 600, "zombieSpawnDensity": "100 100"}, "mission_216": {"backgroundName": "set2_roadworks2", "cameraOverlayColor": "145 124 105 255", "coins": "20 7", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_roadworks", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_boomer z_builder z_builder 3 z_naked  z_boomer z_naked z_puke z_chopper 15", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_drug 20", "set": 2, "startWave": "z_boomer z_builder z_builder z_builder ", "water": "40 25", "weather": "Sandstorm", "xp": "57 9", "zombieBaseHp": 600, "zombieSpawnDensity": "100 100"}, "mission_217": {"backgroundName": "set2_cafe", "cameraOverlayColor": "145 124 105 255", "coins": "20 7", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_barrels", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "13 z_nakedRun z_nakedRun z_nakedRun 13 z_nakedRun z_nakedRun z_nakedRun ", "mainWaveRepeats": 3, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 2, "startWave": "z_boss", "water": "40 25", "xp": "57 9", "zombieBaseHp": 600, "zombieSpawnDensity": "100 100"}, "mission_218": {"backgroundName": "set2_roadworks", "coins": "40 12", "extraReward": " Van1(item_drug 1; item_tablets 1)", "extraWave": "Van1(44 188);", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_barrels", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "30 z_hunter z_hunter z_hunter z_hunter z_hunter z_hunter 20 z_megavolt z_megavolt z_paramedic z_paramedic z_cop z_cop z_cop z_cop z_cop 20 z_hunter z_hunter z_hunter z_hunter z_hunter z_hunter 20 z_hunter z_hunter z_hunter z_hunter z_hunter z_hunter 20 z_hunter z_hunter z_hunter z_hunter z_hunter z_hunter 20 z_hunter z_hunter z_hunter z_hunter z_hunter z_hunter 999  ", "mainWaveRepeats": 3, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_bread 60; item_powder 10", "set": 2, "startWave": "z_cop z_cop z_cop z_puke z_puke z_puke z_puke z_naked z_naked z_naked z_builder z_builder z_builder z_builder z_builder z_cop z_cop ", "unlock_stars": 90, "water": "40 25", "xp": "57 9", "zombieBaseHp": 600, "zombieSpawnDensity": "100 100"}, "mission_219": {"backgroundName": "set2_fence", "coins": "40 12", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set2_barrels", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "30 z_hunter z_hunter z_hunter z_hunter z_hunter z_hunter 20 z_cop z_cop z_cop 20 z_hunter z_hunter z_hunter z_hunter z_hunter z_hunter 20 z_hunter z_hunter z_hunter z_hunter z_hunter z_hunter 20 z_hunter z_hunter z_hunter z_hunter z_hunter z_hunter 20 z_hunter z_hunter z_hunter z_hunter z_hunter z_hunter 999  ", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_radio 100; item_drug 20; item_powder 10", "set": 2, "startWave": "z_cop z_cop z_cop z_puke z_naked z_naked z_naked z_naked z_naked z_naked z_naked z_builder z_builder z_builder z_builder z_builder z_builder z_boomer", "unlock_stars": 97, "water": "40 25", "xp": "57 9", "zombieBaseHp": 600, "zombieSpawnDensity": "100 100"}, "mission_301": {"backgroundName": "set3_street1", "cameraOverlayColor": "97 100 159 255", "coins": "20 7", "extraReward": "CUV1(item_tablets 1)", "extraWave": "CUV1(76 186; z_copsmall)", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_fence", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "3 b_naked 1 b_naked 8", "mainWaveRepeats": 3, "missionComplexity": 2, "missionObjects": [{"Name": "BanditCap", "X": 22, "Z": 46}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "b_naked", "water": "90 25", "xp": "57 9", "zombieBaseHp": 700, "zombieSpawnDensity": "100 100"}, "mission_302": {"backgroundName": "set3_street2", "coins": "20 7", "extraReward": "Van1(item_chocolate 1)", "extraWave": "Van1(26 198)", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_wall", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 b_naked 1 b_naked 1 b_naked 7 b_farmer 7", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "b_naked ", "water": "90 25", "xp": "57 9", "zombieBaseHp": 700, "zombieSpawnDensity": "100 100"}, "mission_303": {"backgroundName": "set3_street1", "cameraOverlayColor": "97 100 159 255", "coins": "23 8", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_brokenfence", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 b_naked 7 b_builder b_biker b_builder 18 b_farmer", "mainWaveRepeats": 3, "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_bread 20", "set": 3, "startWave": "b_builder", "water": "90 25", "weather": "Rain set3_puddle", "xp": "67 10", "zombieBaseHp": 700, "zombieSpawnDensity": "100 100"}, "mission_304": {"backgroundName": "set3_militaryzone", "coins": "23 8", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_militaryzone", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_nakedRun z_bulletproof 10 z_bulletproof z_bulletproof z_cop 12 z_bulletproof 12 ", "mainWaveRepeats": 3, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "z_bulletproof z_bulletproof", "water": "90 25", "xp": "67 10", "zombieBaseHp": 700, "zombieSpawnDensity": "100 100"}, "mission_305": {"backgroundName": "set3_wall", "coins": "23 8", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_cars", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_nakedRun z_soldierfat 15 z_soldierfat 16  z_soldierfat 23 z_nakedRun 15 z_soldierfat 17  z_soldierfat 19", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "z_<PERSON><PERSON>t", "water": "90 25", "xp": "67 10", "zombieBaseHp": 700, "zombieSpawnDensity": "100 100"}, "mission_306": {"backgroundName": "set3_wall", "coins": "23 8", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_fencewithcar", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_nakedRun 9 z_naked z_soldier<PERSON>t z_soldier<PERSON>t 17 z_bulletproof 10 z_witch 5 z_puke z_soldierfat z_puke z_bulletproof 22", "mainWaveRepeats": 3, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "z_naked z_naked z_soldier<PERSON>t", "water": "90 25", "xp": "67 10", "zombieBaseHp": 700, "zombieSpawnDensity": "100 100"}, "mission_307": {"backgroundName": "set3_street2", "cameraOverlayColor": "97 100 159 255", "coins": "23 8", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_policecars", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "7 z_hunter 1 z_copsmall 1 z_copsmall 2 z_copsmall z_copsmall 15 z_hunter z_hunter 5", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_brush 20", "set": 3, "startWave": "z_<PERSON><PERSON>l", "water": "90 25", "weather": "Rain set3_puddle", "xp": "67 10", "zombieBaseHp": 700, "zombieSpawnDensity": "100 100"}, "mission_308": {"backgroundName": "set3_street1", "coins": "23 8", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_empty", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "7 z_soldier 1 z_soldier 2 z_copsmall 3 z_copsmall z_copsmall 18 z_soldier z_hunter 5", "mainWaveRepeats": 3, "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "z_soldier z_soldier z_soldier", "water": "90 25", "xp": "67 10", "zombieBaseHp": 700, "zombieSpawnDensity": "100 100"}, "mission_309": {"backgroundName": "set3_militaryzone", "coins": "23 8", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_militaryzone", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_boomer z_bulletproof z_bulletproof 4 z_copsmall z_boomer z_copsmall z_bulletproof z_bulletproof 25", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "z_boomer ", "water": "90 25", "xp": "67 10", "zombieBaseHp": 700, "zombieSpawnDensity": "100 100"}, "mission_310": {"backgroundName": "set3_quarantine", "coins": "23 8", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_fence", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_nakedRun 5 z_yellow z_yellow z_soldier<PERSON>t 9", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_tablets 20", "set": 3, "startWave": "z_yellow z_yellow", "water": "90 25", "xp": "67 10", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_311": {"backgroundName": "set3_quarantine", "coins": "23 8", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_fence", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_nakedRun 6 z_naked z_yellowFat z_yellowFat 13 z_bulletproof z_witch 5 z_yellow z_yellow 21", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "z_yellowFat", "water": "90 25", "xp": "67 10", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_312": {"backgroundName": "set3_brokenstreet", "cameraOverlayColor": "97 100 159 255", "coins": "23 8", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_empty", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "17 z_hunter z_hunter 1 z_hunter 1 z_hunter 6 z_soldier<PERSON>t  z_soldier<PERSON>t 20", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_chocolate 10", "set": 3, "startWave": "z_soldierfat z_soldierfat", "water": "90 25", "weather": "Rain set3_puddle", "xp": "67 10", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_313": {"backgroundName": "set3_brokenstreet", "coins": "23 8", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_empty", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_copsmall z_soldierfat 15 z_soldierfat 25 z_kamikaze z_kamikaze 10", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "z_kamikaze", "water": "90 25", "xp": "67 10", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_314": {"backgroundName": "set3_quarantine", "cameraOverlayColor": "97 100 159 255", "coins": "23 8", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_fence", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_nakedRun 9 z_naked z_yellowFat z_yellowFat 13 z_bulletproof z_hunter z_hunter 10 z_hunter  z_yellow z_soldierfat z_yellow z_bulletproof 22 ", "mainWaveRepeats": 3, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "z_yellowFat", "water": "90 25", "weather": "Rain set3_puddle", "xp": "67 10", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_315": {"backgroundName": "set3_brokenstreet", "coins": "27 9", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_empty", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_nakedRun 7 z_naked z_soldier<PERSON>t z_soldierfat 17 z_bulletproof 7 z_kamikaze z_kamikaze 5 z_puke z_soldierfat z_puke z_bulletproof 24", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "z_kamikaze", "water": "90 25", "xp": "77 11", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_316": {"backgroundName": "set3_camp", "coins": "27 9", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_camp", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_nakedRun z_soldierfat 15 z_soldierfat z_soldierfat 16  z_shielder 23 z_nakedRun 15 z_soldierfat 17  z_soldier<PERSON>t 19", "mainWaveRepeats": 3, "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "z_shielder", "water": "90 25", "xp": "77 11", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_317": {"backgroundName": "set3_camp", "coins": "27 9", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_camp", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_nakedRun 8 z_naked z_soldier<PERSON>t z_soldierfat 16 z_bulletproof 8 z_shielder z_witch 4 z_puke z_soldierfat z_puke z_bulletproof 23", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "z_shielder", "water": "90 25", "xp": "77 11", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_318": {"backgroundName": "set3_quarantine", "cameraOverlayColor": "97 100 159 255", "coins": "27 9", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_fence", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_hunter 6 z_naked z_yellowFat z_yellowFat 15 z_bulletproof z_witch z_witch 7 z_yellow z_yellow 24", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "z_yellowFat", "water": "90 25", "weather": "Rain set3_puddle", "xp": "77 11", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_319": {"backgroundName": "set3_wall", "coins": "27 9", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_fencewithcar", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_nakedRun z_soldierfat 15 z_soldierfat z_soldierfat 16  z_shielder z_shielder 23 z_nakedRun 15 z_soldierfat 17  z_soldierfat z_shielder 19", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_brush 20", "set": 3, "startWave": "z_shielder z_bulletproof", "water": "90 25", "xp": "77 11", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_320": {"backgroundName": "set3_brokenstreet", "coins": "27 9", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_empty", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_boomer z_shielder z_bulletproof z_bulletproof 7 z_copsmall z_boomer 4 z_copsmall z_bulletproof z_bulletproof 27", "mainWaveRepeats": 3, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "z_boomer z_shielder ", "water": "90 25", "xp": "77 11", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_321": {"backgroundName": "set3_street2", "cameraOverlayColor": "127 148 127 255", "coins": "27 9", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_wall", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "1 b_naked 3 b_naked 3 b_builder 2 b_naked b_farmer 2 b_naked 3 b_naked 3 b_builder 2 b_naked b_naked b_cap 1", "mainWaveRepeats": 3, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 3, "startWave": "b_naked b_naked b_naked b_naked ", "water": "90 25", "xp": "77 11", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_322": {"backgroundName": "set3_quarantine", "coins": "54 18", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_fence", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "30 z_kamikaze z_kamikaze z_kamikaze z_kamikaze z_kamikaze 30 z_boomer z_boomer z_boomer z_soldierfat z_soldierfat z_soldierfat 20 z_kamikaze z_kamikaze z_kamikaze z_kamikaze z_kamikaze z_kamikaze z_boomer 20 z_kamikaze z_kamikaze z_kamikaze  z_kamikaze z_kamikaze z_boomer 20 z_kamikaze z_kamikaze z_kamikaze z_kamikaze z_kamikaze z_kamikaze z_kamikaze z_boomer 999", "mainWaveRepeats": 3, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_chocolate 60; item_brush 10", "set": 3, "startWave": "z_yellowFat z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow", "unlock_stars": 160, "water": "90 25", "xp": "77 11", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_323": {"backgroundName": "set3_quarantine", "coins": "54 18", "finalWave": "5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch 5 z_hunter z_witch z_hunter z_witch z_hunter 1 z_witch z_hunter z_hunter 1 z_witch z_hunter z_witch z_hunter z_witch 1 z_hunter z_witch z_hunter z_witch z_witch", "foregroundName": "set3_fence", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_skeletonMillitary 3 z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary z_cheechmarin z_cheechmarin z_cheechmarin z_cheechmarin z_paramedic z_paramedic z_paramedic z_paramedic 40 z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary 20", "mainWaveRepeats": 3, "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_chocolate 100; item_tablets 20; item_powder 10", "set": 3, "startWave": "z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_yellowFat", "unlock_stars": 168, "water": "90 25", "xp": "77 11", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_401": {"backgroundName": "set4_road", "coins": "27 9", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_road", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "18 z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary 10 z_soldierfat z_soldierfat 26", "mainWaveRepeats": 3, "missionComplexity": 1, "missionObjects": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 162, "Z": 48}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 154, "Z": 10}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "X": 167, "Z": -6}], "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 4, "startWave": "z_copsmall z_copsmall ", "water": "90 25", "xp": "77 11", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_402": {"backgroundName": "set4_quarantine", "coins": "27 9", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_quarantine", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_undead-5 z_soldier 19 z_soldier ", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_bread 20", "set": 4, "startWave": "z_undead", "water": "90 25", "xp": "77 11", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_403": {"backgroundName": "set4_road", "coins": "27 9", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_road", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_skeletonMillitary z_soldierfat 15 z_soldierfat z_soldierfat 16  z_shielder 23 z_skeletonMillitary 15 z_soldierfat 17 z_soldierfat z_shielder 19", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "6 z_nakedRun z_soldierfat 15 z_soldierfat z_soldierfat 16  z_shielder z_shielder 23 z_nakedRun 15 z_soldierfat 17  z_soldierfat z_shielder 19", "set": 4, "startWave": "z_shielder z_bulletproof", "water": "90 25", "xp": "77 11", "zombieBaseHp": 800, "zombieSpawnDensity": "100 100"}, "mission_404": {"backgroundName": "set4_firestation", "coins": "27 9", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_firestation", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_firefighter z_undead z_undead z_undead 20", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 4, "startWave": "z_firefighter", "water": "90 25", "xp": "77 11", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_405": {"backgroundName": "set4_mall1", "cameraOverlayColor": "97 100 159 255", "coins": "27 9", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_mall1", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_skeletonMillitary 6 z_skeletonSapper 7 z_cop z_cop z_cheechmarin z_cheechmarin z_cheechmarin z_cheechmarin 15 z_skeletonMillitary z_skeletonMillitary 11 z_cheechmarin 11 z_witch 12 z_witch z_witch", "mainWaveRepeats": 3, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 4, "startWave": "z_copsmall z_copsmall ", "water": "90 25", "weather": "Rain set4_puddle", "xp": "77 11", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_406": {"backgroundName": "set4_road", "coins": "27 9", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_road", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_copsmall z_soldierfat 15 z_soldierfat 25 z_kamikaze z_kamikaze 10", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_brush 20", "set": 4, "startWave": "z_kamikaze", "water": "90 25", "xp": "77 11", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_407": {"backgroundName": "set4_shop1", "coins": "27 9", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_shop1", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_demon 19 z_hunter z_hunter z_hunter z_undead z_hunter z_hunter z_hunter z_demon 19", "mainWaveRepeats": 3, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 4, "startWave": "z_demon ", "water": "90 25", "xp": "77 11", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_408": {"backgroundName": "set4_brokenstreet", "coins": "27 9", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_brokenstreet", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_demon z_skeletonMillitary z_skeletonMillitary z_copsmall z_copsmall z_copsmall 27", "mainWaveRepeats": 3, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 4, "startWave": "z_demon ", "water": "90 25", "xp": "77 11", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_409": {"backgroundName": "set4_street1", "coins": "27 9", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_street1", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_demon 18 z_nakedRun z_nakedRun z_nakedRun z_undead z_hunter z_nakedRun z_nakedRun z_demon 20", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_tablets 20", "set": 4, "startWave": "z_demon ", "water": "90 25", "xp": "77 11", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_410": {"backgroundName": "set4_road", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_road", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_boomer z_undead z_skeletonSapper z_skeletonSapper 4 z_copsmall z_boomer z_undead z_copsmall z_skeletonSapper z_skeletonSapper z_skeletonMillitary 25", "mainWaveRepeats": 3, "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 4, "startWave": "z_boomer z_boomer ", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_411": {"backgroundName": "set4_shop2", "cameraOverlayColor": "97 100 159 255", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_shop2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_skeletonMillitary 5 z_cheechmarin z_yellowFat z_yellowFat 11 z_skeletonSapper z_witch 3 z_yellow z_yellow 19 ", "mainWaveRepeats": 3, "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 4, "startWave": "z_yellowFat", "water": "90 25", "weather": "Rain set4_puddle", "xp": "90 12", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_412": {"backgroundName": "set4_road", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_road", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_hunter z_soldierfat 7 z_soldierfat  z_soldierfat z_megavolt 8 z_copsmall z_copsmall 15 z_hunter z_soldierfat  7 z_soldierfat  z_soldierfat  8 z_copsmall z_copsmall 15 z_hunter z_soldierfat  8 z_soldierfat  z_soldierfat  9 z_copsmall z_copsmall 9", "mainWaveRepeats": 3, "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_chocolate 10", "set": 4, "startWave": "z_bulletproof", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_413": {"backgroundName": "set4_firestation", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_firestation", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_firefighter z_undead z_undead z_undead z_shielder 23 z_firefighter z_undead z_undead z_undead 20", "mainWaveRepeats": 3, "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 4, "startWave": "z_firefighter", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_414": {"backgroundName": "set4_church", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_church", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_demon 15 z_skeletonMillitary 10 z_skeletonMillitary z_skeletonMillitary z_demon 10 z_skeletonMillitary z_skeletonMillitary 35  ", "mainWaveRepeats": 3, "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 4, "spawnUnderTheRoad": "z_blackSkeleton 3", "startWave": "z_blackSkeleton z_blackSkeleton z_blackSkeleton", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_415": {"backgroundName": "set4_church", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_church", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_skeletonMillitary 10 z_skeletonMillitary z_skeletonMillitary z_necromancer 10 z_skeletonMillitary z_skeletonMillitary 15  ", "mainWaveRepeats": 3, "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 4, "spawnUnderTheRoad": "z_blackSkeleton 3", "startWave": "z_necromancer", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_416": {"backgroundName": "set4_mall2", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_mall2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "12 z_cop z_cop 8 z_cop z_yellowFat z_cop ", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 4, "startWave": "z_cop z_cop ", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_417": {"backgroundName": "set4_street2", "cameraOverlayColor": "97 100 159 255", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_street2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_nakedRun z_nakedRun z_nakedRun 6 z_cheechmarin z_yellowFat z_yellowFat 11 z_skeletonSapper z_witch z_witch 4 z_yellow z_yellow 19 ", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 4, "startWave": "z_yellowFat z_yellowFat", "water": "90 25", "weather": "Rain set4_puddle", "xp": "90 12", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_418": {"backgroundName": "set4_street3", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_street3", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_shielder 5 z_undead z_undead z_undead z_undead z_undead 22", "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_brush 20", "set": 4, "startWave": "z_undead z_undead", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_419": {"backgroundName": "set4_church", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_church", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_skeletonMillitary 10 z_skeletonMillitary z_skeletonMillitary z_demon z_necromancer z_demon 10 z_skeletonMillitary z_skeletonMillitary 15  ", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 4, "spawnUnderTheRoad": "z_blackSkeleton 2", "startWave": "z_necromancer", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_420": {"backgroundName": "set4_quarantine", "coins": "62 21", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_quarantine", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_skeletonMillitary 3 z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary z_cheechmarin z_cheechmarin z_cheechmarin z_cheechmarin z_paramedic z_paramedic z_paramedic z_paramedic 30 z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary z_skeletonMillitary 10", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_chocolate 60; item_brush 10", "set": 4, "startWave": "z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_paramedic z_yellowFat", "unlock_stars": 220, "water": "90 25", "xp": "90 12", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_421": {"backgroundName": "set4_quarantine", "coins": "62 21", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set4_quarantine", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "30 z_kamikaze z_kamikaze z_kamikaze z_kamikaze z_kamikaze 20 z_soldierfat z_soldierfat z_soldierfat 15 z_kamikaze z_kamikaze z_kamikaze z_kamikaze z_kamikaze z_kamikaze 15 z_kamikaze z_kamikaze z_kamikaze  z_kamikaze z_kamikaze 15 z_kamikaze z_kamikaze z_kamikaze z_kamikaze z_kamikaze z_kamikaze z_kamikaze 999  ", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_chocolate 100; item_tablets 20; item_powder 10", "set": 4, "startWave": "z_yellowFat z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow", "unlock_stars": 225, "water": "90 25", "xp": "90 12", "zombieBaseHp": 1200, "zombieSpawnDensity": "100 100"}, "mission_501": {"backgroundName": "set5_road", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_road", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "4 z_boomer z_builder z_builder 3 z_naked  z_boomer z_naked z_puke z_puke z_builder z_builder z_builder 10", "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "startWave": "z_boomer ", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_502": {"backgroundName": "set5_trailers1", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_trailers1", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_nakedRun 5 z_naked z_yellowFat z_yellowFat 9 z_bulletproof z_witch 3 z_yellow z_yellow 16 ", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "startWave": "z_yellowFat", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_503": {"backgroundName": "set5_street5", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_street5", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "7 z_bulletproof 1 z_copsmall 1 z_copsmall 2 z_copsmall z_copsmall 12 z_bulletproof z_hunter 2 z_kamikaze 3", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_bread 20", "set": 5, "startWave": "z_copsmall z_bulletproof ", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_504": {"backgroundName": "set5_trailers2", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_trailers2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_nakedRun 8 z_naked z_yellowFat z_yellowFat 10 z_bulletproof z_hunter z_hunter 11 z_hunter  z_yellow z_soldierfat z_yellow z_bulletproof 16", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "startWave": "z_yellowFat", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_505": {"backgroundName": "set5_street1", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_street1", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_skeletonMillitary z_soldierfat 12 z_soldierfat z_soldierfat 13 z_shielder 17 z_skeletonMillitary 12 z_soldierfat 14 z_soldierfat z_shielder 15", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "startWave": "z_shielder z_bulletproof", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_506": {"backgroundName": "set5_trailers1", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_trailers1", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_skeletonMillitary 5 z_cheechmarin z_yellowFat z_yellowFat 10 z_bulletproof z_witch 3 z_yellow z_yellow 16 z_kamikaze 3", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "startWave": "z_yellowFat", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_507": {"backgroundName": "set5_trailers2", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_trailers2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_nakedRun z_nakedRun z_nakedRun 7 z_cheechmarin z_yellowFat z_yellowFat 11 z_bulletproof z_witch z_witch 6 z_yellow z_yellow 17", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_brush 20", "set": 5, "startWave": "z_yellowFat z_yellowFat", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_508": {"backgroundName": "set5_street2", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_street2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_hunter z_skeletonMillitary  z_soldierfat 7 z_soldierfat  z_soldierfat z_megavolt 8 z_copsmall z_copsmall 13 z_hunter z_skeletonMillitary  z_soldierfat 7 z_soldierfat  z_soldierfat 7 z_copsmall z_copsmall 13 z_hunter z_soldierfat 7 z_soldierfat  z_soldierfat  9 z_copsmall z_copsmall 9", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "startWave": "z_bulletproof", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_509": {"backgroundName": "set5_church", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_church", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_skeletonMillitary 12 z_skeletonMillitary z_skeletonMillitary z_demon z_necromancer z_demon 10 z_skeletonMillitary z_skeletonMillitary 14 ", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "spawnUnderTheRoad": "z_blackSkeleton 2", "startWave": "z_blackSkeleton z_necromancer z_blackSkeleton z_blackSkeleton", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_510": {"backgroundName": "set5_plane", "coins": "31 10", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_plane", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_boomer z_undead z_bulletproof z_bulletproof 4 z_copsmall z_boomer z_undead z_copsmall z_bulletproof z_bulletproof z_skeletonMillitary 16", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_tablets 20", "set": 5, "startWave": "z_boomer z_boomer ", "water": "90 25", "xp": "90 12", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_511": {"backgroundName": "set5_street3", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_street3", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_copsmall z_soldierfat 15 z_soldierfat 20 z_kamikaze z_kamikaze 10", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "startWave": "z_kamikaze", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_512": {"backgroundName": "set5_bridge", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_bridge", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_hunter z_cop 6 z_cop z_cop z_megavolt 7 z_puke z_puke 12 z_hunter z_cop 5 z_cop z_cop 6 z_puke z_puke 12 z_hunter z_cop 5 z_cop z_cop 6 z_puke z_puke 7 ", "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_chocolate 10", "set": 5, "startWave": "z_builder", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_513": {"backgroundName": "set5_street2", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_street2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_demon z_skeletonMillitary z_skeletonMillitary z_copsmall z_copsmall z_copsmall 17", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "startWave": "z_demon ", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_514": {"backgroundName": "set5_street4", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_street4", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_copsmall z_soldierfat 15 z_soldierfat 22 z_kamikaze z_kamikaze z_skeletonMillitary 10", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "startWave": "z_kamikaze", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_515": {"backgroundName": "set5_trailers2", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_trailers2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_skeletonMillitary 5 z_cheechmarin z_yellowFat z_yellowFat 11 z_bulletproof z_witch 3 z_yellow z_yellow 17 ", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "startWave": "z_yellowFat", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_516": {"backgroundName": "set5_church", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_church", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_demon 13 z_skeletonMillitary 9 z_skeletonMillitary z_skeletonMillitary z_demon 9 z_skeletonMillitary z_skeletonMillitary 27  ", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "spawnUnderTheRoad": "z_blackSkeleton 2", "startWave": "z_blackSkeleton z_necromancer z_blackSkeleton z_blackSkeleton", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_517": {"backgroundName": "set5_street5", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_street5", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_demon 15 z_hunter z_hunter z_hunter 5 z_undead z_hunter z_hunter z_hunter z_demon 12", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "startWave": "z_demon ", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_518": {"backgroundName": "set5_mall", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_mall", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "10 z_cop z_cop 8 z_cop z_yellowFat z_cop 1", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "startWave": "z_cop z_cop ", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_519": {"backgroundName": "set5_street1", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_street1", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "12 z_hunter z_hunter z_hunter z_hunter 5 z_soldier<PERSON>t  z_soldier<PERSON>t 14", "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_brush 20", "set": 5, "startWave": "z_<PERSON><PERSON>t", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_520": {"backgroundName": "set5_firestation", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_firestation", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_firefighter z_undead z_undead z_undead z_shielder 18 z_firefighter z_undead z_undead z_undead 15", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "startWave": "z_firefighter", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_521": {"backgroundName": "set5_bridge2", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_bridge2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "7 b_naked 9 b_builder b_builder b_cap b_builder b_farmer 18 b_farmer b_cap 2", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 5, "startWave": "b_cap", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_522": {"backgroundName": "set5_street4", "coins": "72 24", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_street4", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "25 z_witch z_witch z_witch z_witch z_witch 20 z_boomer z_boomer z_boomer z_soldierfat z_soldierfat z_soldierfat 17 z_witch z_witch z_witch z_witch z_witch z_boomer 15 z_witch z_witch z_witch z_witch z_witch  z_boomer 15 z_witch z_witch z_witch z_witch z_witch  z_boomer 15 z_witch z_witch z_witch z_witch z_witch  z_boomer 15 z_witch z_witch z_witch z_witch z_witch  z_boomer 15 z_witch z_witch z_witch z_witch z_witch z_witch 999  ", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_chocolate 60; item_brush 10", "set": 5, "startWave": "z_yellowFat z_yellowFat z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow", "unlock_stars": 290, "water": "90 25", "xp": "105 13", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_523": {"backgroundName": "set5_street5", "coins": "72 24", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set5_street5", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "25 z_witch z_witch z_witch z_witch z_witch 20 z_yellowFat z_yellowFat z_yellowFat z_soldierfat z_soldierfat z_soldierfat 17 z_witch z_witch z_witch z_witch z_witch z_boomer 15 z_witch z_witch z_witch z_witch z_witch  z_boomer 15 z_witch z_witch z_witch z_witch z_witch  z_boomer 15 z_witch z_witch z_witch z_witch z_witch  z_boomer 15 z_witch z_witch z_witch z_witch z_witch  z_boomer 15 z_witch z_witch z_witch z_witch z_witch z_witch 999", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_chocolate 100; item_tablets 20; item_powder 10", "set": 5, "startWave": "z_yellowFat z_yellowFat z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow z_yellow", "unlock_stars": 297, "water": "90 25", "xp": "105 13", "zombieBaseHp": 1500, "zombieSpawnDensity": "100 100"}, "mission_601": {"backgroundName": "set6_road1", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_road1", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_monk z_monk z_monk z_monk z_monk 5 z_chineseExpl z_chineseExpl z_soldierfat z_soldierfat 27 z_monk z_monk z_monk z_monk z_monk 5 z_chineseExpl z_chineseExpl z_soldierfat z_soldierfat 12 z_monk z_monk z_monk z_monk z_monk 5 z_chineseExpl z_chineseExpl z_soldierfat z_soldierfat  10 z_monk z_monk z_monk z_monk z_monk 5 z_chineseExpl z_chineseExpl z_soldierfat z_soldierfat 12 z_monk z_monk z_monk z_monk z_monk 5 z_chineseExpl z_chineseExpl z_soldierfat z_soldierfat 10 z_monk z_monk z_monk z_monk z_monk 5 z_chineseExpl z_chineseExpl z_soldierfat z_soldierfat 999", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_chineseExpl-4 z_chinese-5", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_602": {"backgroundName": "set6_street2", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_street2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_undead z_undead z_undead z_undead z_undead 19 z_monk z_monk z_monk z_monk z_monk z_undead z_undead z_undead z_undead z_undead 19 z_monk z_monk z_monk z_monk z_monk z_undead z_undead z_undead z_undead z_undead 19 z_monk z_monk z_monk z_monk z_monk z_undead z_undead z_undead z_undead z_undead 19 z_monk z_monk z_monk z_monk z_monk z_undead z_undead z_undead z_undead z_undead 19 z_monk z_monk z_monk z_monk z_monk z_undead z_undead z_undead z_undead z_undead 19 z_monk z_monk z_monk z_monk z_monk z_undead z_undead z_undead z_undead z_undead 19 z_monk z_monk z_monk z_monk z_monk 999", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_undead-5", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_603": {"backgroundName": "set6_street11", "coins": "36 12", "extraReward": "ChinaMinivan1(item_bread 1)", "extraWave": "ChinaMinivan1(-145 190; z_chineseExpl)", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_street11", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "7 z_monk z_monk z_soldier<PERSON>t z_chinese z_chineseExpl z_chinese 10 z_monk z_soldier<PERSON>t z_soldierfat 12 z_chinese z_monk z_monk z_chineseExpl z_chinese z_shielder 14 z_monk z_monk 10 z_soldierfat 12 z_soldier<PERSON>t z_shielder 19 z_monk z_monk z_soldierfat z_chinese z_chineseExpl z_chinese 10 z_monk z_soldierfat z_soldierfat 12 z_chinese z_monk z_monk z_chineseExpl z_chinese z_shielder 14 z_monk z_monk 10 z_soldierfat 12 z_soldierfat z_shielder 19 z_monk z_monk z_soldierfat z_chinese z_chineseExpl z_chinese 10 z_monk z_soldierfat z_soldierfat 12 z_chinese z_monk z_monk z_chineseExpl z_chinese z_shielder 14 z_monk z_monk 10 z_soldierfat 12 z_soldierfat z_shielder 999", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_chineseExpl-4 z_chinese-5", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_604": {"backgroundName": "set6_street3", "coins": "36 12", "extraReward": "Matiz1(item_tablets 1)", "extraWave": "Matiz1(-64 180; z_cop)", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_street3", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_chineseExpl z_chineseExpl z_chineseExpl z_monk z_soldierfat 12 z_soldierfat 20 z_kamikaze z_kamikaze 10 z_chineseExpl z_chineseExpl z_chineseExpl z_monk z_soldierfat 10 z_soldierfat 9 z_kamikaze z_kamikaze 9", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_drug 20", "set": 6, "startWave": "z_chineseExpl-4 z_chinese-5", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_605": {"backgroundName": "set6_street1", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_street1", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_monk 5 z_bulletproof 5 z_cop z_cop z_cheechmarin z_cheechmarin z_cheechmarin z_cheechmarin 10 z_monk z_monk 9 z_cheechmarin 9 z_witch 10 z_witch z_witch", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_chineseExpl-4 z_cheechmarin-5", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_606": {"backgroundName": "set6_hospital", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_hospital", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "7 z_monk 5 z_monk z_monk z_monk z_cheechmarin z_cheechmarin z_cheechmarin z_cheechmarin z_paramedic z_paramedic z_paramedic z_paramedic 23 z_monk z_monk z_monk z_monk z_monk z_monk z_monk 12", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_paramedic-16 z_yellowFat-2", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_607": {"backgroundName": "set6_street4", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_street4", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_demon 12 z_monk z_monk z_monk z_undead z_monk z_monk z_monk z_demon 13 z_demon 10 z_monk z_monk z_monk z_undead z_monk z_monk z_monk z_demon 10 z_demon 10 z_monk z_monk z_monk z_undead z_monk z_monk z_monk z_demon 10 z_demon 10 z_monk z_monk z_monk z_undead z_monk z_monk z_monk z_demon 10 z_demon 10 z_monk z_monk z_monk z_undead z_monk z_monk z_monk z_demon 10", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_chineseExpl-4 z_chinese-5", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_608": {"backgroundName": "set6_street10", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_street10", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_monk z_monk z_monk z_monk z_monk 5 z_psy z_psy 12 z_monk z_monk z_monk z_monk z_monk 5 z_psy z_psy 9 z_monk z_monk z_monk z_monk z_monk 5 z_psy z_psy 8 z_monk z_monk z_monk z_monk z_monk 5 z_psy z_psy 8 z_monk z_monk z_monk z_monk z_monk 5 z_psy z_psy 7 z_monk z_monk z_monk z_monk z_monk 5 z_psy z_psy 7 z_monk z_monk z_monk z_monk z_monk z_monk z_monk 5 z_psy z_psy 7 999", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_stew 20", "set": 6, "startWave": "z_chineseExpl-4 z_psy-2", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_609": {"backgroundName": "set6_street5", "coins": "36 12", "extraReward": "ChinaMinitruck1(item_brush 1)", "extraWave": "ChinaMinitruck1(25 180);", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_street5", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "3 z_boomer z_undead z_bulletproof z_bulletproof 2 z_chineseExpl z_chineseExpl z_chinese z_chinese z_chinese z_boomer z_undead z_bulletproof z_bulletproof z_monk z_monk z_monk 12", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_chineseExpl-4 z_chinese-5", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_610": {"backgroundName": "set6_road2", "coins": "36 12", "extraReward": "Matiz1(item_radio 1)", "extraWave": "Matiz1(40 175; z_chineseExpl)", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_road2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_monk z_monk z_monk 5 z_chineseExpl z_soldierfat z_soldierfat 10 z_bulletproof 5 z_shielder z_witch 4 z_rotten z_rotten z_rotten z_soldierfat z_rotten z_bulletproof 18", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_chineseExpl-4 z_rotten-5", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_611": {"backgroundName": "set6_policestation2", "coins": "36 12", "extraReward": "Matiz1(item_stew 1)", "extraWave": "Matiz1(8 175);", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_policestation2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_monk z_monk z_monk z_monk z_monk 5 z_psy z_psy z_soldierfat z_soldierfat 27 z_monk z_monk z_monk z_monk z_monk 5 z_psy z_psy z_soldierfat z_soldierfat 12 z_monk z_monk z_monk z_monk z_monk 5 z_psy z_psy z_soldierfat z_soldierfat  10 z_monk z_monk z_monk z_monk z_monk 5 z_psy z_psy z_soldierfat z_soldierfat 12 z_monk z_monk z_monk z_monk z_monk 5 z_psy z_psy z_soldierfat z_soldierfat 10 z_monk z_monk z_monk z_monk z_monk 5 z_psy z_psy z_soldierfat z_soldierfat 999", "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_powder 20", "set": 6, "startWave": "z_chineseExpl-4 z_psy-3", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_612": {"backgroundName": "set6_street7", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_street7", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "7 z_monk z_monk 8 z_naked z_yellowFat z_yellowFat 10 z_bulletproof z_hunter z_hunter 11 z_hunter z_hunter z_yellow z_soldierfat z_yellow z_bulletproof 10 z_monk z_monk 8 z_naked z_yellowFat z_yellowFat 9 z_bulletproof z_hunter z_hunter 10 z_hunter z_hunter z_yellow z_soldierfat z_yellow z_bulletproof 8 z_monk z_monk 7 z_naked z_yellowFat z_yellowFat 7 z_bulletproof z_hunter z_hunter 7 z_hunter z_hunter z_yellow z_soldierfat z_yellow z_bulletproof 8 z_monk z_monk 7 z_naked z_yellowFat z_yellowFat 7 z_hunter z_hunter 7 z_hunter z_hunter z_yellow z_soldierfat z_yellow 999", "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_chineseExpl-2 z_rotten-5 z_chinese-2", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_613": {"backgroundName": "set6_road3", "coins": "36 12", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_road3", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "12 z_hunter z_hunter z_hunter z_hunter 5 z_soldier<PERSON>t  z_soldier<PERSON>t 14", "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_chineseExpl-4 z_chinese-5", "water": "90 25", "xp": "105 13", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_614": {"backgroundName": "set6_street12", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_street12", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_cop z_cop 7 z_cop z_yellowFat z_cop ", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_chineseExpl-2 z_cop-2 z_chinese-2", "water": "90 25", "xp": "122 15", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_615": {"backgroundName": "set6_hospital2", "coins": "43 14", "extraReward": "ChinaMinitruck1(item_drug 1)", "extraWave": "ChinaMinitruck1(28 190);", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_hospital2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_monk 5 z_monk z_monk z_monk z_monk z_chineseExpl z_chineseExpl z_chinese z_chinese z_paramedic z_paramedic z_paramedic z_paramedic 18 z_monk z_monk z_monk z_monk z_monk z_monk z_monk 11", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_paramedic-16 z_yellowFat-2", "water": "90 25", "xp": "122 15", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_616": {"backgroundName": "set6_street11", "coins": "43 14", "extraReward": "ChinaMinivan1(item_powder 1)", "extraWave": "ChinaMinivan1(-145 190; z_kamikaze)", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_street11", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_undead z_undead z_undead z_undead z_psy 19 z_monk z_monk z_monk z_monk z_monk z_undead z_undead z_undead z_undead z_psy 19 z_monk z_monk z_monk z_monk z_monk z_undead z_undead z_undead z_undead z_psy z_psy 19 z_monk z_monk z_monk z_monk z_monk z_undead z_undead z_undead z_undead z_psy z_psy 19 z_monk z_monk z_monk z_monk z_monk z_undead z_undead z_undead z_undead z_psy z_psy 19 z_monk z_monk z_monk z_monk z_monk z_undead z_undead z_undead z_undead z_psy z_psy 19 z_monk z_monk z_monk z_monk z_monk z_undead z_undead z_undead z_undead z_psy z_psy 19 z_monk z_monk z_monk z_monk z_monk z_psy 999", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_undead-2 z_psy-3", "water": "90 25", "xp": "122 15", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_617": {"backgroundName": "set6_policestation", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_policestation", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_copsmall z_soldierfat 12 z_soldierfat 17 z_kamikaze z_kamikaze 10", "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_soldierfat-4", "water": "90 25", "xp": "122 15", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_618": {"backgroundName": "set6_street8", "coins": "43 14", "extraReward": "ChinaMinitruck1(item_chocolate 1)", "extraWave": "ChinaMinitruck1(0 190; z_chineseExpl)", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_street8", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_shielder 5 z_undead z_undead z_undead z_undead z_undead 22", "missionComplexity": 0, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_chineseExpl-4 z_chinese-5 z_shielder-1", "water": "90 25", "xp": "122 15", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_619": {"backgroundName": "set6_street6", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_street6", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_preAlien z_preAlien  z_preAlien  z_preAlien 5 z_psy z_psy z_soldierfat z_soldierfat 27 z_preAlien  z_preAlien  z_preAlien  z_preAlien  z_preAlien 5 z_psy z_psy z_soldierfat z_soldierfat 15 z_preAlien  z_preAlien  z_preAlien  z_preAlien  z_preAlien 5 z_psy z_psy z_soldierfat z_soldierfat 15 z_preAlien  z_preAlien  z_preAlien  z_preAlien  z_preAlien  5 z_psy z_psy z_soldierfat z_soldierfat 15 z_preAlien  z_preAlien  z_preAlien  z_preAlien  z_preAlien  5 z_psy z_psy z_soldierfat z_soldierfat 10 z_preAlien  z_preAlien  z_preAlien  z_preAlien  z_preAlien  5 z_psy z_psy z_soldierfat z_soldierfat 999", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_chocolate 20", "set": 6, "startWave": "z_chineseExpl-4 z_chinese-5", "water": "90 25", "xp": "122 15", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_620": {"backgroundName": "set6_street7", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_street7", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_monk 3 z_monk z_monk z_monk z_cheechmarin z_cheechmarin z_cheechmarin z_cheechmarin z_paramedic z_paramedic z_paramedic z_paramedic 20 z_preAlien z_preAlien z_preAlien z_preAlien z_monk z_monk z_monk z_monk z_monk z_monk 10", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_chineseExpl-4 z_chinese-5", "water": "90 25", "xp": "122 15", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_621": {"backgroundName": "set6_road4", "coins": "43 14", "extraReward": "ChinaMinivan1(item_tablets 1)", "extraWave": "ChinaMinivan1(-64 180; z_chineseExpl)", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_road4", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_firefighter z_undead z_undead z_undead z_shielder 13 z_firefighter z_undead z_undead z_undead 12", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_firefighter-3", "water": "90 25", "xp": "122 15", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_622": {"backgroundName": "set6_street9", "coins": "43 14", "extraReward": "Matiz1(item_bread 1)", "extraWave": "Matiz1(45 165; z_bulletproof)", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_street9", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "3 z_boomer z_undead z_bulletproof z_bulletproof 2 z_chineseExpl z_chineseExpl z_chinese z_chinese z_chinese z_boomer z_undead z_bulletproof z_bulletproof z_preAlien z_preAlien z_monk 12", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 6, "startWave": "z_chineseExpl-4 z_chinese-5", "water": "90 25", "xp": "122 15", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_623": {"backgroundName": "set6_dragon", "coins": "86 29", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_dragon", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "15 z_preAlien 1 z_preAlien 2 z_preAlien z_preAlien z_preAlien z_preAlien z_preAlien 5 z_cop z_cop 19 z_preAlien z_preAlien z_preAlien ", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_chocolate 60; item_brush 10", "set": 6, "startWave": "z_chineseExpl-4 z_chinese-5", "unlock_stars": 351, "water": "90 25", "xp": "122 15", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_624": {"backgroundName": "set6_dragon", "coins": "86 29", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set6_dragon", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "15 z_preAlien 1 z_preAlien 2 z_preAlien z_preAlien z_preAlien z_preAlien z_preAlien 5 z_soldierfat z_soldierfat 19 z_preAlien z_preAlien z_preAlien ", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "rewardItems": "item_chocolate 100; item_tablets 20; item_powder 10", "set": 6, "startWave": "z_chineseExpl-4 z_chinese-5", "unlock_stars": 356, "water": "90 25", "xp": "122 15", "zombieBaseHp": 1900, "zombieSpawnDensity": "100 100"}, "mission_701": {"backgroundName": "set7_gasstation", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_gasstation", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_putrid z_putrid z_putrid z_copsmall z_soldierfat 15 z_soldierfat 19 z_kamikaze z_kamikaze 12", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_putrid-2", "water": "90 25", "xp": "122 15", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_702": {"backgroundName": "set7_train", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_train", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "6 z_alienNakedRun 3 z_alienNakedRun  z_alienNaked z_alienNaked z_alienNaked z_alienNaked z_paramedic z_paramedic z_paramedic z_paramedic 20 z_preAlien z_preAlien z_preAlien z_preAlien z_alienNakedRun  z_alienNakedRun 10", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_alienNaked-4", "water": "90 25", "xp": "122 15", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_703": {"backgroundName": "set7_angars", "cameraOverlayColor": "97 100 159 255", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_angars", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "12 z_alienNakedRun z_alienNakedRun z_alienNakedRun 5 z_alienCop z_alienCop 14", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_alienCop-2", "water": "90 25", "weather": "Rain set7_puddle", "xp": "122 15", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_704": {"backgroundName": "set7_street1", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_street1", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_preAlien z_preAlien  z_preAlien  z_preAlien 5 z_psy z_psy z_alienCop z_alienCop 27 z_preAlien  z_preAlien  z_preAlien  z_preAlien  z_preAlien 5 z_psy z_psy z_alienCop z_alienCop 15 z_preAlien  z_preAlien  z_preAlien  z_preAlien  z_preAlien 5 z_psy z_psy z_alienCop z_alienCop 15 z_preAlien  z_preAlien  z_preAlien  z_preAlien  z_preAlien  5 z_psy z_psy z_alienCop z_alienCop 15 z_preAlien  z_preAlien  z_preAlien  z_preAlien  z_preAlien  5 z_psy z_psy z_alienCop z_alienCop 10 z_preAlien  z_preAlien  z_preAlien  z_preAlien  z_preAlien  5 z_psy z_psy z_alienCop z_alienCop 999", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_alienCop-2 z_preAlien-2", "water": "90 25", "xp": "122 15", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_705": {"backgroundName": "set7_street2", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_street2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_undead z_undead z_undead z_undead z_psy 19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_psy 19 z_alienNaked z_alienNakedRun z_undead z_undead z_undead z_undead z_psy z_psy 19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_psy z_psy 19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_psy z_psy 19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_psy z_psy 19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_psy z_psy 19 z_alienNakedRun z_alienNakedRun z_alienNaked z_alienNakedRun z_psy 999", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_alienCop-2 z_undead-1", "water": "90 25", "xp": "122 15", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_706": {"backgroundName": "set7_street9", "cameraOverlayColor": "97 100 159 255", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_street9", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_alienCop z_alienCop 7 z_alienCop z_yellowFat z_alienCop ", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_alienCop-2 z_yellowFat-2", "water": "90 25", "weather": "Rain set7_puddle", "xp": "122 15", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_707": {"backgroundName": "set7_street5", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_street5", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_undead z_undead z_undead z_undead z_kamikaze  19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_kamikaze  19 z_alienNaked z_alienNakedRun z_undead z_undead z_undead z_undead z_kamikaze  z_kamikaze  19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_kamikaze  z_kamikaze  19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_kamikaze  z_kamikaze  19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_kamikaze  z_kamikaze  19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_kamikaze  z_kamikaze  19 z_alienNakedRun z_alienNakedRun z_alienNaked z_alienNakedRun z_kamikaze  999", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_soldierfat-4", "water": "90 25", "xp": "122 15", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_708": {"backgroundName": "set7_road1", "cameraOverlayColor": "97 100 159 255", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_road1", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_alienCop 7 z_alienCop z_alienCop 9 z_insect z_alienCop 7 z_alienCop z_alienCop 9 z_alienCop 7 z_alienCop z_alienCop z_alienCop 7 z_alienCop z_alienCop 9", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_alienNaked-4", "water": "90 25", "weather": "Rain set7_puddle", "xp": "122 15", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_709": {"backgroundName": "set7_street4", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_street4", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_alienCop 7 z_alienCop z_alienCop 9 z_insect z_alienCop 7 z_alienCop z_alienCop 9 z_alienCop 7 z_alienCop z_alienCop z_alienCop 7 z_insect z_alienCop z_alienCop 9", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_alienNaked-4", "water": "90 25", "xp": "122 15", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_710": {"backgroundName": "set7_train2", "cameraOverlayColor": "97 100 159 255", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_train2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_alienCop 7 z_alienCop z_alienCop 9 z_insect z_alienCop 7 z_alienCop z_alienCop 9 z_insect z_alienCop 7 z_alienCop z_alienCop z_alienCop 7 z_insect z_alienCop z_alienCop 9", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_alienNaked-4", "water": "90 25", "weather": "Rain set7_puddle", "xp": "122 15", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_711": {"backgroundName": "set7_aliens1", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_aliens1", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_putrid z_putrid z_putrid z_copsmall z_soldierfat 15 z_insect z_soldierfat 19 z_kamikaze z_kamikaze 12", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_putrid-3", "water": "90 25", "xp": "122 15", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_712": {"backgroundName": "set7_street3", "coins": "43 14", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_street3", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_alienCop z_alienCop 7 z_alienCop z_sphere z_alienCop ", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_sphere-1", "water": "90 25", "xp": "122 15", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_713": {"backgroundName": "set7_road2", "coins": "56 19", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_road2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_undead z_undead z_undead z_undead z_sphere 19 z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere 19 z_alienNaked z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere z_sphere 19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere z_sphere 19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere z_sphere 19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere z_sphere 19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere z_sphere 19 z_alienNakedRun z_alienNakedRun z_alienNaked z_sphere 999", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_sphere-1", "water": "90 25", "xp": "168 21", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_714": {"backgroundName": "set7_aliens2", "coins": "56 19", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_aliens2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_putrid z_putrid z_putrid z_copsmall z_soldierfat z_sphere 15 z_insect z_soldierfat 19 z_kamikaze z_kamikaze 12", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_putrid-3", "water": "90 25", "xp": "168 21", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_715": {"backgroundName": "set7_street6", "coins": "56 19", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_street6", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_insect z_alienCop z_alienCop 7 z_alienCop z_sphere z_alienCop ", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_sphere-2", "water": "90 25", "xp": "168 21", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_716": {"backgroundName": "set7_street7", "coins": "56 19", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_street7", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "10 z_kamikaze", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_egg-1", "water": "90 25", "xp": "168 21", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_717": {"backgroundName": "set7_aliens3", "coins": "56 19", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_aliens3", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "14 z_kamikaze", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_egg-3", "water": "90 25", "xp": "168 21", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_718": {"backgroundName": "set7_street8", "coins": "56 19", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_street8", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_undead z_undead z_undead z_undead z_sphere 19 z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere 19 z_alienNaked z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere z_sphere 19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere z_sphere 19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere z_sphere 19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere z_sphere 19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere z_sphere 19 z_alienNakedRun z_alienNakedRun z_alienNaked z_sphere 999", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_egg-2", "water": "90 25", "xp": "168 21", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_719": {"backgroundName": "set7_street10", "coins": "56 19", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_street10", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_alienCop 17 z_alienCop z_preEgg  z_sphere z_alienCop 9 z_alienCop 17 z_alienCop z_sphere z_alienCop", "missionComplexity": 2, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_egg-2", "water": "90 25", "xp": "168 21", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_720": {"backgroundName": "set7_challenge2", "coins": "56 19", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_challenge2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_putrid z_putrid z_copsmall z_preEgg z_soldierfat 15 z_soldierfat 19 z_kamikaze 12 z_putrid z_putrid z_copsmall z_soldierfat 15 z_soldierfat 19 z_kamikaze z_kamikaze 12", "missionComplexity": 1, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_egg-1", "water": "90 25", "xp": "168 21", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_721": {"backgroundName": "set7_challenge1", "coins": "56 19", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set7_challenge1", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_insect z_alienCop 7 z_preEgg  z_sphere z_alienCop 15 z_insect z_alienCop 7 z_sphere z_alienCop 9 z_insect z_alienCop 7 z_sphere z_alienCop 15 z_insect z_alienCop 7 z_sphere z_alienCop ", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 7, "startWave": "z_egg-2", "water": "90 25", "xp": "168 21", "zombieBaseHp": 2400, "zombieSpawnDensity": "100 100"}, "mission_801": {"backgroundName": "set8_church2", "coins": "56 19", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set8_church2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_copsmall 9 z_kamikaze z_insect z_crank 10 z_kamikaze z_kamikaze 7 z_insect  z_crank z_kamikaze 3", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 8, "startWave": "z_crank-1", "water": "90 25", "xp": "168 21", "zombieBaseHp": 3000, "zombieSpawnDensity": "100 100"}, "mission_802": {"backgroundName": "set8_corrupted2", "coins": "56 19", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set8_corrupted2", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_crank z_putrid z_prisoner z_prisoner 9 z_kamikaze z_kamikaze z_insect z_insect 1", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 8, "startWave": "z_prisoner-3", "water": "90 25", "xp": "168 21", "zombieBaseHp": 3000, "zombieSpawnDensity": "100 100"}, "mission_803": {"backgroundName": "set8_corrupted3", "coins": "56 19", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set8_corrupted3", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_undead z_undead z_undead z_undead z_kamikaze 15 z_prisonerLocusts z_undead z_undead z_undead z_undead z_kamikaze  12 z_alienNaked z_prisonerLocusts z_undead z_undead z_undead z_undead z_kamikaze  z_kamikaze  12 z_prisonerLocusts z_prisonerLocusts z_undead z_undead z_undead z_undead z_kamikaze  z_kamikaze  12 z_prisonerLocusts z_undead z_undead z_undead z_undead z_kamikaze  z_kamikaze  12 z_prisonerLocusts z_prisonerLocusts z_prisonerLocusts z_undead z_undead z_undead z_undead z_kamikaze  z_kamikaze  12 z_prisonerLocusts z_prisonerLocusts z_prisonerLocusts z_undead z_undead z_undead z_undead z_kamikaze  z_kamikaze  12 z_prisonerLocusts z_prisonerLocusts z_alienNaked z_prisonerLocusts z_kamikaze  999", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 8, "startWave": "z_crank-1 z_prisoner-1", "water": "90 25", "xp": "168 21", "zombieBaseHp": 3000, "zombieSpawnDensity": "100 100"}, "mission_804": {"backgroundName": "set8_corrupted1", "coins": "56 19", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set8_corrupted1", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_twins 7 z_twins z_twins 9 z_insect z_twins 7 z_twins z_twins z_insect 9 z_twins 7 z_twins z_insect z_twins z_twins 7 z_insect z_twins z_twins 9", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 8, "startWave": "z_twins-1", "water": "90 25", "xp": "168 21", "zombieBaseHp": 3000, "zombieSpawnDensity": "100 100"}, "mission_805": {"backgroundName": "set8_corrupted4", "coins": "56 19", "finalWave": "5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch 5 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_skeletonMillitary 1 z_witch z_skeletonMillitary z_skeletonMillitary 1 z_witch z_skeletonMillitary z_witch z_skeletonMillitary z_witch 1 z_skeletonMillitary z_witch z_skeletonMillitary z_witch z_witch", "foregroundName": "set8_corrupted4", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "9 z_insect z_swat 7 z_preEgg  z_sphere z_swat 15 z_insect z_swat 7 z_sphere z_swat 9 z_insect z_swat 7 z_sphere z_swat 15 z_insect z_swat 7 z_sphere z_swat ", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 8, "startWave": "z_egg-2", "water": "90 25", "xp": "168 21", "zombieBaseHp": 3000, "zombieSpawnDensity": "100 100"}, "mission_806": {"backgroundName": "set8_policestation", "coins": "56 19", "finalWave": "5 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_prisonerLocusts 1 z_witch z_prisonerLocusts z_prisonerLocusts 1 z_witch z_prisonerLocusts z_witch z_prisonerLocusts z_witch 1 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_witch 5 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_prisonerLocusts 1 z_witch z_prisonerLocusts z_prisonerLocusts 1 z_witch z_prisonerLocusts z_witch z_prisonerLocusts z_witch 1 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_witch 5 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_prisonerLocusts 1 z_witch z_prisonerLocusts z_prisonerLocusts 1 z_witch z_prisonerLocusts z_witch z_prisonerLocusts z_witch 1 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_witch", "foregroundName": "set8_policestation", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "5 z_swat 9 z_kamikaze z_insect z_crank 10 z_kamikaze 7 z_insect  z_crank 3 z_preEgg 5 z_swat 9 z_kamikaze z_insect z_crank 10 z_kamikaze 7 z_insect  z_crank 8 z_swat 9 z_kamikaze z_insect z_crank 10 z_kamikaze 7 z_insect  z_crank 8 z_swat 9 z_kamikaze z_insect z_crank 10 z_kamikaze 7 z_insect  z_crank 8 z_swat 9 z_kamikaze z_insect z_crank 10 z_kamikaze 7 z_insect  z_crank z_kamikaze 3 ", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 8, "startWave": "z_egg-2", "water": "90 25", "xp": "168 21", "zombieBaseHp": 3000, "zombieSpawnDensity": "100 100"}, "mission_807": {"backgroundName": "set8_military1", "coins": "56 19", "finalWave": "5 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_prisonerLocusts 1 z_witch z_prisonerLocusts z_prisonerLocusts 1 z_witch z_prisonerLocusts z_witch z_prisonerLocusts z_witch 1 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_witch 5 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_prisonerLocusts 1 z_witch z_prisonerLocusts z_prisonerLocusts 1 z_witch z_prisonerLocusts z_witch z_prisonerLocusts z_witch 1 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_witch 5 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_prisonerLocusts 1 z_witch z_prisonerLocusts z_prisonerLocusts 1 z_witch z_prisonerLocusts z_witch z_prisonerLocusts z_witch 1 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_witch", "foregroundName": "set8_military1", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "12 z_kamikaze z_kamikaze", "missionComplexity": 3, "missionTime": 180, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 8, "startWave": "z_egg-3", "water": "90 25", "xp": "168 21", "zombieBaseHp": 3000, "zombieSpawnDensity": "100 100"}, "mission_808": {"backgroundName": "set8_prison", "coins": "56 19", "finalWave": "5 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_prisonerLocusts 1 z_witch z_prisonerLocusts z_prisonerLocusts 1 z_witch z_prisonerLocusts z_witch z_prisonerLocusts z_witch 1 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_witch 5 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_prisonerLocusts 1 z_witch z_prisonerLocusts z_prisonerLocusts 1 z_witch z_prisonerLocusts z_witch z_prisonerLocusts z_witch 1 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_witch 5 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_prisonerLocusts 1 z_witch z_prisonerLocusts z_prisonerLocusts 1 z_witch z_prisonerLocusts z_witch z_prisonerLocusts z_witch 1 z_prisonerLocusts z_witch z_prisonerLocusts z_witch z_witch", "foregroundName": "set8_prison", "inBattleReward": "coins 1 10 10", "isAdditional": false, "mainWave": "z_undead z_undead z_undead z_undead z_sphere 19 z_alienNakedRun z_alienNakedRun z_kamikaze z_kamikaze z_undead z_undead z_undead z_undead z_sphere 19 z_alienNaked z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere z_sphere 19 z_alienNakedRun z_alienNakedRun z_kamikaze z_kamikaze z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere z_sphere 19 z_alienNakedRun z_alienNakedRun z_kamikaze z_kamikaze z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere z_sphere 19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_kamikaze z_kamikaze z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere z_sphere 19 z_alienNakedRun z_alienNakedRun z_alienNakedRun z_undead z_undead z_undead z_undead z_sphere z_sphere 19 z_alienNakedRun z_alienNakedRun z_kamikaze z_kamikaze z_alienNaked z_sphere z_preEgg", "missionComplexity": 3, "missionTime": 9999999, "noiseLevel": 100000, "noiseWave": "z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun z_nakedRun", "set": 8, "startWave": "z_cephalopoda z_egg-2", "water": "90 25", "xp": "168 21", "zombieBaseHp": 6000, "zombieSpawnDensity": "100 100"}, "MissionsParallax_702_Forest": "part3", "MissionsParallax_710_Forest": "part3", "MissionsParallax_List": "702 710", "param_AdVictoryBreakCooldownMinutes": 1, "param_AdWeightAdColony": 0, "param_AdWeightUnityAds": 1, "param_bossMissions": "321 419", "param_BuffUnlockStars": 3, "param_CraftCost": 100, "param_CraftCostCommonItem": 50, "param_CraftCostRareItem": 75, "param_CrossPromoDA_Cooldown": 1440, "param_CrossPromoDA_Cooldown@dev": 0.33, "param_CrossPromoDA_Stars": 60, "param_DailyBonusDuration": 86400, "param_KTPlayUnlockStars": 35, "param_L10nUrl": "https://dazw-dl.mobirate.com/l10n/2.6.1.6.gz", "param_MaxBuffCount": 31, "param_MaxStamina": 20, "param_notifyExplorationAwaitsTime": 10, "param_PVPUnlockStars": 37, "param_PVPWaterAmount": 100, "param_RankToUnlockUnitReset": 4, "param_RouletteUnlockStrars": 999, "param_staminaMinutesToGain": 10, "param_StaminaPerFacebookRequest": 4, "param_StaminaSellerPricePerOne": 50, "param_staminaSpendBattle": 2, "param_staminaSpendSideBattle": 5, "param_StaminaSpendTile": 1, "param_TimeBoxesClocksReward": "shop_box1 1", "param_TimeBoxesMaxClocks": 15, "param_TimeBoxesRefundAmountPerDay": 4, "param_timeBoxesUnlockStars": 15, "param_UnitResetPrice": 1000, "param_ValuePacksMinRank": 999, "param_VideoBoxCooldownMinutes": 720, "param_VideoBoxCooldownMinutes@dev": 1, "param_VideoBoxCountView": 3, "param_VideoCoinsCooldownMinutes": 480, "param_VideoCoinsCooldownMinutes@dev": 1, "param_VideoCoinsMaxRewardsInARow": 3, "param_VideoCoinsReward": "Coins 50", "param_VideoCoinsRewardBonus": "Coins 25", "param_VideoLowStamina": 2, "param_VideoRewardFirstThreeStars": "Coins 50", "param_VideoRewardFirstVictory": "Coins 25", "param_VideoStaminaReward": "Stamina 2", "param_VideoUnlockStars": 25, "param_WebConfigABTestChance": 0, "param_WeeklyEventsHideButtonTime": "4:00:00", "param_WeeklyEventsHideButtonTime@dev": "0:05:00", "param_WeeklyEventSoonToBeCompleted": "3:00:00", "param_WeeklyEventSoonToBeCompleted@dev": "0:10:00", "param_WeeklyEventsTopRequestCooldown": "1:00:00", "param_WeeklyEventsTopRequestCooldown@dev": "0:00:10", "param_WeeklyEventsUnlockStars": 40, "param_WeeklyEventsUnlockStars@dev": 40, "PVPLeague_1": {"CoolReward": "shop_box1 1", "EndBattleReward": "coins 3", "InBattleReward": "RandomItems", "InBattleRewardChance": 4, "MaxInBattleRewards": 1, "SimpleReward": "coins 30"}, "PVPLeague_10": {"CoolReward": "shop_box1 5", "EndBattleReward": "coins 20", "InBattleReward": "RandomItems", "InBattleRewardChance": 4, "MaxInBattleRewards": 1, "SimpleReward": "coins 200"}, "PVPLeague_2": {"CoolReward": "shop_box1 1", "EndBattleReward": "coins 4", "InBattleReward": "RandomItems", "InBattleRewardChance": 4, "MaxInBattleRewards": 1, "SimpleReward": "coins 40"}, "PVPLeague_3": {"CoolReward": "shop_box1 1", "EndBattleReward": "coins 5", "InBattleReward": "RandomItems", "InBattleRewardChance": 4, "MaxInBattleRewards": 1, "SimpleReward": "coins 50"}, "PVPLeague_4": {"CoolReward": "shop_box1 1", "EndBattleReward": "coins 7", "InBattleReward": "RandomItems", "InBattleRewardChance": 4, "MaxInBattleRewards": 1, "SimpleReward": "coins 70"}, "PVPLeague_5": {"CoolReward": "shop_box1 1", "EndBattleReward": "coins 8", "InBattleReward": "RandomItems", "InBattleRewardChance": 4, "MaxInBattleRewards": 1, "SimpleReward": "coins 80"}, "PVPLeague_6": {"CoolReward": "shop_box1 1", "EndBattleReward": "coins 9", "InBattleReward": "RandomItems", "InBattleRewardChance": 4, "MaxInBattleRewards": 1, "SimpleReward": "coins 90"}, "PVPLeague_7": {"CoolReward": "shop_box1 2", "EndBattleReward": "coins 12", "InBattleReward": "RandomItems", "InBattleRewardChance": 4, "MaxInBattleRewards": 1, "SimpleReward": "coins 120"}, "PVPLeague_8": {"CoolReward": "shop_box1 3", "EndBattleReward": "coins 14", "InBattleReward": "RandomItems", "InBattleRewardChance": 4, "MaxInBattleRewards": 1, "SimpleReward": "coins 140"}, "PVPLeague_9": {"CoolReward": "shop_box1 4", "EndBattleReward": "coins 17", "InBattleReward": "RandomItems", "InBattleRewardChance": 4, "MaxInBattleRewards": 1, "SimpleReward": "coins 170"}, "quest_1": {"description": "quest.description.kill_200", "reward": "xp 5; coins 10", "target": 200, "type": "Kill", "unlockCondition": "1 99", "unlockStars": 999}, "quest_10": {"description": "quest.description.kill_15", "lockStars": 66, "reward": "xp 90; coins 90", "target": 15, "type": "Kill", "unlockStars": 9}, "quest_11": {"description": "quest.description.kill_45", "lockStars": 155, "reward": "xp 210; coins 165", "target": 45, "type": "Kill", "unlockStars": 66}, "quest_12": {"description": "quest.description.kill_90", "lockStars": 230, "reward": "xp 315; coins 210", "target": 90, "type": "Kill", "unlockStars": 155}, "quest_13": {"description": "quest.description.kill_120", "reward": "xp 450; coins 315", "target": 120, "type": "Kill", "unlockStars": 230}, "quest_14": {"description": "quest.description.winmissions_3", "lockStars": 66, "reward": "xp 90; coins 90", "target": 3, "type": "WinMissions", "unlockStars": 6}, "quest_15": {"description": "quest.description.winmissions_9", "lockStars": 155, "reward": "xp 210; coins 165", "target": 9, "type": "WinMissions", "unlockStars": 66}, "quest_16": {"description": "quest.description.winmissions_18", "lockStars": 230, "reward": "xp 315; coins 210", "target": 18, "type": "WinMissions", "unlockStars": 155}, "quest_17": {"description": "quest.description.winmissions_24", "reward": "xp 450; coins 315", "target": 24, "type": "WinMissions", "unlockStars": 230}, "quest_18": {"description": "quest.description.takestars_9", "lockStars": 66, "reward": "xp 90; coins 90", "target": 9, "type": "TakeStars", "unlockStars": 18}, "quest_19": {"description": "quest.description.takestars_18", "lockStars": 155, "reward": "xp 210; coins 165", "target": 18, "type": "TakeStars", "unlockStars": 66}, "quest_2": {"description": "quest.description.barrelKill_30", "reward": "xp 5; coins 10", "target": 30, "type": "BarrelKill", "unlockCondition": "1 99", "unlockStars": 999}, "quest_20": {"description": "quest.description.takestars_54", "lockStars": 230, "reward": "xp 315; coins 210", "target": 54, "type": "TakeStars", "unlockStars": 155}, "quest_21": {"description": "quest.description.takestars_72", "lockStars": 392, "reward": "xp 450; coins 315", "target": 72, "type": "TakeStars", "unlockStars": 230}, "quest_3": {"description": "quest.description.woDamage_12", "param": 205, "reward": "xp 20; shop_box1 1", "target": 1, "type": "Win3", "unlockCondition": "2 99", "unlockStars": 999}, "quest_4": {"description": "quest.description.notUseIn_13", "param": 13, "reward": "xp 60; coins 10", "target": 1, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unlockCondition": "3 99", "unlockStars": 999}, "quest_5": {"description": "quest.description.spendfuel_6", "reward": "xp 180; coins 60", "target": 20, "type": "SpendFuel", "unlockStars": 24}, "quest_6": {"description": "quest.description.generate_600", "reward": "xp 60; coins 10", "target": 600, "type": "Generator", "unlockCondition": "3 99", "unlockStars": 999}, "quest_7": {"description": "quest.description.<PERSON><PERSON><PERSON><PERSON>", "param": "item_molotov", "reward": "xp 60; coins 10", "target": 16, "type": "Use", "unlockCondition": "3 99", "unlockStars": 999}, "quest_8": {"description": "quest.description.findItem_1", "reward": "xp 60; coins 10", "target": 1, "type": "FindItem", "unlockCondition": "3 99", "unlockStars": 999}, "quest_9": {"description": "quest.description.findItem_5", "reward": "xp 60; coins 10", "target": 5, "type": "FindItem", "unlockCondition": "3 99", "unlockStars": 999}, "rank_1": {"XP": 0}, "rank_10": {"reward": "item_powder 1; stamina 4", "XP": 5334}, "rank_11": {"reward": "item_chocolate 1; stamina 4", "XP": 8032}, "rank_12": {"reward": "shop_box1 1; stamina 4", "XP": 12056}, "rank_13": {"reward": "item_stew 1; stamina 4", "XP": 18058}, "rank_14": {"reward": "item_drug 1; stamina 4", "XP": 27014}, "rank_15": {"reward": "item_powder 1; stamina 4", "XP": 40373}, "rank_16": {"reward": "shop_box1 1; stamina 4", "XP": 60304}, "rank_17": {"reward": "item_chocolate 1; stamina 4", "XP": 90036}, "rank_18": {"reward": "item_stew 1; stamina 4", "XP": 134392}, "rank_19": {"reward": "shop_box1 1; stamina 4", "XP": 200563}, "rank_2": {"reward": "item_brush 1; stamina 4", "XP": 54}, "rank_3": {"reward": "item_tablets 1; stamina 4", "XP": 184}, "rank_4": {"reward": "item_brush 1; stamina 4", "XP": 348}, "rank_4@dev": {"reward": "item_tablets 1; stamina 4", "XP": 348}, "rank_5": {"reward": "item_bread 1; stamina 4", "XP": 592}, "rank_5@dev": {"reward": "item_tablets 1; stamina 4", "XP": 592}, "rank_6": {"reward": "item_stew 1; stamina 4", "XP": 957}, "rank_7": {"reward": "item_stew 1; stamina 4", "XP": 1502}, "rank_8": {"reward": "shop_box1 1; stamina 4", "XP": 2314}, "rank_9": {"reward": "item_drug 1; stamina 4", "XP": 3526}, "rouletteReward_1": {"Reward": "coins 50", "Weight": 30}, "rouletteReward_10": {"Reward": "coins 100", "Weight": 15}, "rouletteReward_11": {"Reward": "RandomBuffs 1", "Weight": 15}, "rouletteReward_12": {"Reward": "RandomItems 1 LegendaryItem", "Weight": 4}, "rouletteReward_2": {"Reward": "coins 500", "Weight": 5}, "rouletteReward_3": {"Reward": "RandomBuffs 2", "Weight": 10}, "rouletteReward_4": {"Reward": "shop_box1 1", "Weight": 3}, "rouletteReward_5": {"Reward": "RandomItems 3", "Weight": 5}, "rouletteReward_6": {"Reward": "coins 250", "Weight": 7}, "rouletteReward_7": {"Reward": "RandomItems 1", "Weight": 17}, "rouletteReward_8": {"Reward": "stamina 3", "Weight": 10}, "rouletteReward_9": {"Reward": "shop_box1 3", "Weight": 2}, "seller_buyPercentStep": 0, "seller_drop_1": "shop_box1 1", "seller_drop_2": "RandomCarParts 1", "seller_drop_3": "RandomItems 1", "seller_drop_4": "RandomItems 1", "seller_militaryKit": 500, "seller_sellPercentStep": 10, "seller_starsToUnlock": 32, "SellerLimit_rank_1": {"CoinsForBuy": 300, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_10": {"CoinsForBuy": 1200, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_11": {"CoinsForBuy": 1300, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_12": {"CoinsForBuy": 1400, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_13": {"CoinsForBuy": 1500, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_14": {"CoinsForBuy": 1600, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_15": {"CoinsForBuy": 1700, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_16": {"CoinsForBuy": 1800, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_17": {"CoinsForBuy": 1900, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_18": {"CoinsForBuy": 2000, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_19": {"CoinsForBuy": 2100, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_2": {"CoinsForBuy": 400, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_20": {"CoinsForBuy": 2200, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_3": {"CoinsForBuy": 500, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_4": {"CoinsForBuy": 600, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_5": {"CoinsForBuy": 700, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_6": {"CoinsForBuy": 800, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_7": {"CoinsForBuy": 900, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_8": {"CoinsForBuy": 1000, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "SellerLimit_rank_9": {"CoinsForBuy": 1100, "CountItemsForBuy": 999, "CountItemsForSell": 1}, "set_1": {"OpenedTileExp": 1, "Reward": "shop_box1 1;", "Stars": 0}, "set_2": {"OpenedTileExp": 2, "Reward": "shop_box1 1;", "Stars": 28}, "set_3": {"OpenedTileExp": 2, "Reward": "shop_box1 1;", "Stars": 66}, "set_4": {"OpenedTileExp": 2, "Reward": "shop_box1 1;", "Stars": 110}, "set_5": {"OpenedTileExp": 2, "Reward": "shop_box1 1;", "Stars": 155}, "set_6": {"OpenedTileExp": 2, "Reward": "shop_box1 1;", "Stars": 175}, "set_7": {"OpenedTileExp": 2, "Reward": "shop_box1 1;", "Stars": 230}, "set_8": {"OpenedTileExp": 2, "Reward": "shop_box1 1;", "Stars": 280}, "shop_boxCount_legbox": 4, "shop_boxCount_shop_box1": 5, "shop_boxpack1": {"cost": "prem 21", "reward": "shop_box1 1"}, "shop_boxpack2": {"cost": "prem 22", "reward": "shop_box1 2"}, "shop_boxpack3": {"cost": "prem 23", "reward": "shop_box1 3"}, "shop_boxpack4": {"cost": "prem 24", "reward": "shop_box1 4"}, "shop_coinpack1": {"cost": "prem 11", "reward": "coins 100"}, "shop_coinpack2": {"cost": "prem 12", "reward": "coins 101"}, "shop_coinpack3": {"cost": "prem 13", "reward": "coins 102"}, "shop_coinpack4": {"cost": "prem 14", "reward": "coins 103"}, "shop_coinsAmount1": 500, "shop_coinsAmount2": 1350, "shop_coinsAmount3": 4150, "shop_coinsAmount4": 15000, "shopBundle_Agent_currency": "coins 2200", "shopBundle_Agent_packs": 4, "shopBundle_Circus_currency": "coins 6600", "shopBundle_Circus_packs": 13, "shopBundle_CoinsBundle1_currency": "coins 15000", "shopBundle_CoinsBundle1_image": 2, "shopBundle_CoinsBundle2_currency": "coins 4150", "shopBundle_CoinsBundle2_image": 1, "shopBundle_CoinsBundle3_currency": "coins 4150", "shopBundle_CoinsBundle3_image": 1, "shopBundle_CoinsBundle4_currency": "coins 4150", "shopBundle_CoinsBundle4_image": 1, "shopBundle_CoinsBundle5_currency": "coins 1350", "shopBundle_CoinsBundle5_image": 0, "shopBundle_Heist_currency": "coins 2200", "shopBundle_Heist_packs": 4, "shopBundle_PremBundle1_currency": "prem 10", "shopBundle_PremBundle1_image": 2, "shopBundle_PremBundle2_currency": "prem 20", "shopBundle_PremBundle2_image": 1, "shopBundle_PremBundle3_currency": "prem 30", "shopBundle_PremBundle3_image": 1, "shopBundle_PremBundle4_currency": "prem 40", "shopBundle_PremBundle4_image": 1, "shopBundle_PremBundle5_currency": "prem 50", "shopBundle_PremBundle5_image": 0, "shopBundle_Swat_currency": "coins 4000", "shopBundle_Swat_packs": 10, "shopBundle_ValuePack1_currency": "coins 500", "shopBundle_ValuePack1_image": 0, "shopBundle_ValuePack1_legBox": 1, "shopBundle_ValuePack1_packs": 5, "shopBundle_ValuePack2_currency": "coins 4000", "shopBundle_ValuePack2_image": 1, "shopBundle_ValuePack2_legBox": 10, "shopBundle_ValuePack2_packs": 10, "shopBundle_ValuePack3_currency": "coins 4000", "shopBundle_ValuePack3_image": 2, "shopBundle_ValuePack3_legBox": 10, "shopBundle_ValuePack3_packs": 10, "shopBundle_ValuePack4_currency": "coins 14000", "shopBundle_ValuePack4_image": 3, "shopBundle_ValuePack4_legBox": 25, "shopBundle_ValuePack4_packs": 25, "shopReward_legbox_1": {"Guaranteed": true, "Reward": "RandomItems 1 LegendaryItem", "Weight": 0}, "shopReward_legbox_2": {"Guaranteed": true, "Reward": "RandomItems 1 LegendaryItem", "Weight": 0}, "shopReward_legbox_3": {"Guaranteed": true, "Reward": "RandomItems 1 LegendaryItem", "Weight": 0}, "shopReward_legbox_4": {"Guaranteed": true, "Reward": "Stamina 10", "Weight": 0}, "shopReward_shop_box1_1": {"Guaranteed": false, "Reward": "RandomCarParts", "Weight": 1}, "shopReward_shop_box1_2": {"Guaranteed": false, "Reward": "RandomPaints", "Weight": 1}, "shopReward_shop_box1_3": {"Guaranteed": true, "Reward": "RandomItems 1 RareItem", "Weight": 0}, "shopReward_shop_box1_4": {"Guaranteed": false, "Reward": "RandomItems 1 CommonItem RareItem LegendaryItem", "Weight": 32}, "special_minigun": {"ActionTime": 3, "BaseDamage": 0, "Damage": 40}, "tileReward_0": {"position": "5; 3", "reward": "stamina 3;", "set": 1}, "tileReward_1": {"position": "5; 5", "reward": "item_brush 1;", "set": 1}, "tileReward_10": {"position": "-3; -9", "reward": "item_bread 1;", "set": 3}, "tileReward_11": {"position": "4; -9", "reward": "shop_box1 1;", "set": 3}, "tileReward_12": {"position": "1; -14", "reward": "item_drug 1;", "set": 3}, "tileReward_13": {"position": "2; 6", "reward": "stamina 2;", "set": 4}, "tileReward_14": {"position": "8; -2", "reward": "stamina 3;", "set": 4}, "tileReward_15": {"position": "16; 4", "reward": "item_radio 1;", "set": 4}, "tileReward_16": {"position": "9; 5", "reward": "shop_box1 1;", "set": 4}, "tileReward_17": {"position": "2; 0", "reward": "stamina 2;", "set": 5}, "tileReward_18": {"position": "15; -11", "reward": "stamina 2;", "set": 5}, "tileReward_19": {"position": "15; -7", "reward": "shop_box1 1;", "set": 5}, "tileReward_2": {"position": "11; 2", "reward": "shop_box1 1;", "set": 1}, "tileReward_20": {"position": "9; 6", "reward": "stamina 1;", "set": 5}, "tileReward_21": {"position": "8; 6", "reward": "item_powder 1;", "set": 5}, "tileReward_22": {"position": "18; -3", "reward": "item_drug 1;", "set": 5}, "tileReward_23": {"position": "14; -1", "reward": "item_brush 1;", "set": 5}, "tileReward_24": {"position": "6; -10", "reward": "item_chocolate 1;", "set": 6}, "tileReward_25": {"position": "12; -13", "reward": "shop_box1 1;", "set": 6}, "tileReward_26": {"position": "17; -10", "reward": "item_stew 1;", "set": 6}, "tileReward_27": {"position": "3; 3", "reward": "item_powder 1;", "set": 6}, "tileReward_28": {"position": "3; -8", "reward": "shop_box1 1;", "set": 6}, "tileReward_29": {"position": "10; -32", "reward": "shop_box1 1;", "set": 6}, "tileReward_3": {"position": "9; 8", "reward": "item_stew 1;", "set": 1}, "tileReward_30": {"position": "2; 2", "reward": "item_powder 1;", "set": 7}, "tileReward_31": {"position": "5; -3", "reward": "stamina 2;", "set": 7}, "tileReward_32": {"position": "17; -2", "reward": "shop_box1 1;", "set": 7}, "tileReward_33": {"position": "13; -8", "reward": "stamina 2;", "set": 7}, "tileReward_34": {"position": "21; -8", "reward": "item_powder 1;", "set": 7}, "tileReward_35": {"position": "21; -14", "reward": "item_brush 1;", "set": 7}, "tileReward_36": {"position": "24; -14", "reward": "stamina 4;", "set": 7}, "tileReward_37": {"position": "27; -4", "reward": "shop_box1 1;", "set": 7}, "tileReward_38": {"position": "28; -12", "reward": "item_chocolate 1;", "set": 7}, "tileReward_39": {"position": "10; 2", "reward": "item_drug 1;", "set": 7}, "tileReward_4": {"position": "4; -3", "reward": "stamina 2;", "set": 2}, "tileReward_40": {"position": "27; -9", "reward": "stamina 3;", "set": 7}, "tileReward_41": {"position": "3; -2", "reward": "item_chocolate 1;", "set": 7}, "tileReward_42": {"position": "8; -7", "reward": "item_brush 1;", "set": 7}, "tileReward_43": {"position": "3; 1", "reward": "shop_box1 1;", "set": 8}, "tileReward_44": {"position": "9; -3", "reward": "shop_box1 1;", "set": 8}, "tileReward_45": {"position": "14; -5", "reward": "shop_box1 1;", "set": 8}, "tileReward_46": {"position": "14; 0", "reward": "shop_box1 1;", "set": 8}, "tileReward_47": {"position": "7; 1", "reward": "shop_box1 1;", "set": 8}, "tileReward_5": {"position": "5; 4", "reward": "stamina 2;", "set": 2}, "tileReward_6": {"position": "7; 1", "reward": "shop_box1 1;", "set": 2}, "tileReward_7": {"position": "12; 1", "reward": "stamina 3;", "set": 2}, "tileReward_8": {"position": "14; 6", "reward": "shop_box1 1;", "set": 2}, "tileReward_9": {"position": "13; -4", "reward": "item_radio 1;", "set": 2}, "timeBox1_0": {"Duration": 6}, "timeBox1_1": {"Duration": 3}, "timeBox1_2": {"Duration": 8}, "timeBox1_tut": {"Duration": 0.0028}, "timeboxes_points_LegBox": 14, "timeboxes_points_Shop_Box1": 7, "timeboxes_repeats_Shop_Box1": 2, "timeBoxReward1_0_1": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_10": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_11": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_12": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_13": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_14": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_15": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_16": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_17": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_18": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_19": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_2": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_3": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_4": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_5": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_6": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_7": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_8": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_0_9": {"Reward": "RandomItems 2; spins 1"}, "timeBoxReward1_1_1": {"Reward": "RandomItems 1; coins 32; spins 1"}, "timeBoxReward1_1_10": {"Reward": "RandomItems 1; coins 75; spins 1"}, "timeBoxReward1_1_11": {"Reward": "RandomItems 1; coins 78; spins 1"}, "timeBoxReward1_1_12": {"Reward": "RandomItems 1; coins 82; spins 1"}, "timeBoxReward1_1_13": {"Reward": "RandomItems 1; coins 85; spins 1"}, "timeBoxReward1_1_14": {"Reward": "RandomItems 1; coins 88; spins 1"}, "timeBoxReward1_1_15": {"Reward": "RandomItems 1; coins 91; spins 1"}, "timeBoxReward1_1_16": {"Reward": "RandomItems 1; coins 93; spins 1"}, "timeBoxReward1_1_17": {"Reward": "RandomItems 1; coins 96; spins 1"}, "timeBoxReward1_1_18": {"Reward": "RandomItems 1; coins 99; spins 1"}, "timeBoxReward1_1_19": {"Reward": "RandomItems 1; coins 101; spins 1"}, "timeBoxReward1_1_2": {"Reward": "RandomItems 1; coins 39; spins 1"}, "timeBoxReward1_1_3": {"Reward": "RandomItems 1; coins 45; spins 1"}, "timeBoxReward1_1_4": {"Reward": "RandomItems 1; coins 51; spins 1"}, "timeBoxReward1_1_5": {"Reward": "RandomItems 1; coins 55; spins 1"}, "timeBoxReward1_1_6": {"Reward": "RandomItems 1; coins 60; spins 1"}, "timeBoxReward1_1_7": {"Reward": "RandomItems 1; coins 64; spins 1"}, "timeBoxReward1_1_8": {"Reward": "RandomItems 1; coins 68; spins 1"}, "timeBoxReward1_1_9": {"Reward": "RandomItems 1; coins 72; spins 1"}, "timeBoxReward1_2_1": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_10": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_11": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_12": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_13": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_14": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_15": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_16": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_17": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_18": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_19": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_2": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_3": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_4": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_5": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_6": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_7": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_8": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_2_9": {"Reward": "RandomBuffs 2; spins 1"}, "timeBoxReward1_tut_1": {"Reward": "coins 50; spins 1"}, "tip_1": {"context": "loading", "level": 1, "probability": 50}, "tip_10": {"context": "enterthebattlemenu", "level": 7, "probability": 50}, "tip_11": {"context": "enterthebattlemenu", "level": 7, "probability": 50}, "tip_12": {"context": "shop", "level": 1, "probability": 50}, "tip_13": {"context": "unitsupgrademenu", "level": 1, "probability": 50}, "tip_14": {"context": "unitsupgrademenu", "level": 1, "probability": 50}, "tip_15": {"context": "unitscardmenu", "level": 1, "probability": 50}, "tip_16": {"context": "defeat", "level": 1, "probability": 50}, "tip_17": {"context": "defeat", "level": 1, "probability": 50}, "tip_2": {"context": "loading", "level": 1, "probability": 50}, "tip_3": {"context": "enterthebattlemenu", "level": 1, "probability": 50}, "tip_4": {"context": "enterthebattlemenu", "level": 1, "probability": 50}, "tip_5": {"context": "enterthebattlemenu", "level": 7, "probability": 50}, "tip_6": {"context": "enterthebattlemenu", "level": 7, "probability": 50}, "tip_7": {"context": "enterthebattlemenu", "level": 1, "probability": 50}, "tip_8": {"context": "enterthebattlemenu", "level": 7, "probability": 50}, "tip_9": {"context": "enterthebattlemenu", "level": 7, "probability": 50}, "unit_b_biker": {"aggression": 100, "AggressionPenalty": 0.8, "agility": 90, "AgilityPenalty": 0.8, "ammoOnReload": 15, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "bulletDamage": 6, "BulletMaxDist": 350, "BulletSpawnOffsetX": 18, "bulletSpeed": 800, "charges": 1, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 2, "DamageToBase": 2, "explosionResist": 0, "fireResist": 1, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 18, "IdleCount": 1, "meleeResist": 0.8, "Moral": 0, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 6, "needAmmoToShoot": 1, "Noise": 1, "poisonResist": 0, "presentationOrderId": 23, "push": 0, "pushResist": 0.3, "rageCost": 3, "rangeRadius": 145, "rangeResist": 0, "ShootAngle": 25, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 200, "WeaponName": "AK47", "WeaponNoise": 30}, "unit_b_bill": {"aggression": 90, "AggressionPenalty": 0, "agility": 60, "AgilityPenalty": 0, "ammoOnReload": 1, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 0, "AoeX": 18, "BDMoral": 0, "bulletDamage": 10, "BulletMaxDist": 1000, "BulletSpawnOffsetX": 32, "bulletSpeed": 1000, "charges": 1, "cooldown": 0, "cost": 40, "critChance": 0, "damage": 2, "DamageToBase": 0, "explosionResist": 0, "fireResist": 1, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 27, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 0, "MoralDistanceForward": 0, "moveSpeed": 0, "needAmmoToShoot": 1, "Noise": 1, "poisonResist": 0, "presentationOrderId": 1, "push": 0, "pushResist": 1, "rageCost": 0, "rangeRadius": 165, "rangeResist": 0, "ShootAngle": 0, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 200, "WeaponName": "Rifle"}, "unit_b_builder": {"aggression": 3, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 17, "DamageToBase": 8, "explosionResist": 0, "fireResist": 0, "healthPoints": 110, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 20, "push": 10, "pushResist": 0.5, "rageCost": 6, "rangeResist": 0.9, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 80}, "unit_b_cap": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "ammoOnReload": 5, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "bulletDamage": 46, "BulletMaxDist": 350, "BulletSpawnOffsetX": 32, "bulletSpeed": 800, "charges": 1, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 2, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 46, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 4, "needAmmoToShoot": 1, "poisonResist": 0, "presentationOrderId": 21, "push": 10, "pushResist": 0.2, "rageCost": 3, "rangeRadius": 220, "rangeResist": 0, "ShootAngle": 35, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 200, "WeaponName": "Pistol"}, "unit_b_crazy": {"aggression": 50, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 15, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 65, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 22, "push": 0, "pushResist": 0, "rageCost": 1, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_b_farmer": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "ammoOnReload": 2, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "bulletDamage": 10, "BulletMaxDist": 350, "BulletSpawnOffsetX": 20, "bulletSpeed": 800, "charges": 3, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 2, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 43, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 4, "needAmmoToShoot": 1, "poisonResist": 0, "presentationOrderId": 19, "push": 10, "pushResist": 0, "rageCost": 3, "rangeRadius": 180, "rangeResist": 0, "ShootAngle": 50, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 200, "WeaponName": "Shotgun"}, "unit_b_naked": {"aggression": 3, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 15, "DamageToBase": 8, "explosionResist": 0, "fireResist": 0, "healthPoints": 55, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 12, "poisonResist": 0, "presentationOrderId": 18, "push": 10, "pushResist": 0, "rageCost": 3, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 80}, "unit_h_agent": {"aggression": 100, "AggressionPenalty": 0.3, "agility": 90, "AgilityPenalty": 0.5, "ammoOnReload": 10, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 0, "AoeX": 18, "BDMoral": 40, "bulletDamage": 2, "BulletMaxDist": 350, "BulletSpawnOffsetX": 32, "bulletSpeed": 800, "charges": 1, "cooldown": 45, "cost": 20, "critChance": 0, "damage": 6, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 22, "IdleCount": 4, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 120, "MoralDistanceForward": 120, "moveSpeed": 8, "needAmmoToShoot": 1, "Noise": 1, "PlayerRank": 1, "poisonResist": 0, "presentationOrderId": 134, "priorityTargets": "z_boomer z_witch z_dog z_hunter", "push": 0, "pushResist": 0, "rageCost": 0, "rangeRadius": 75, "rangeResist": 0, "ShootAngle": 20, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 150, "WeaponName": "Silencer", "WeaponNoise": 30}, "unit_h_austin": {"aggression": 100, "AggressionPenalty": 0.3, "agility": 90, "AgilityPenalty": 0.5, "ammoOnReload": 10, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 0, "AoeX": 18, "BDMoral": 40, "bulletDamage": 4, "BulletMaxDist": 350, "BulletSpawnOffsetX": 32, "bulletSpeed": 1000, "charges": 1, "cooldown": 30, "cost": 30, "critChance": 0, "damage": 6, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 22, "IdleCount": 4, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 120, "MoralDistanceForward": 120, "moveSpeed": 12, "needAmmoToShoot": 1, "Noise": 1, "PlayerRank": 1, "poisonResist": 0, "presentationOrderId": 122, "priorityTargets": "z_boomer z_witch z_dog z_hunter", "push": 0, "pushResist": 0, "rageCost": 0, "rangeRadius": 75, "rangeResist": 0, "ShootAngle": 20, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 150, "WeaponName": "<PERSON><PERSON>", "WeaponNoise": 30}, "unit_h_ballisticShielder": {"aggression": 90, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 12, "AoeX": 18, "BDMoral": 4, "cooldown": 60, "cost": 35, "critChance": 0.4, "damage": 10, "DamageToBase": 30, "explosionResist": 0, "fireResist": 0, "healthPoints": 130, "IdleCount": 2, "meleeResist": 0, "Moral": 400, "MoralDistanceBack": 40, "MoralDistanceForward": 40, "moveSpeed": 5, "Noise": 3, "PlayerRank": 1, "poisonResist": 1, "presentationOrderId": 102, "priorityTargets": "z_witch z_dog z_hunter", "push": 15, "pushResist": 1, "rageCost": 0, "rangeResist": 0.9, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 80}, "unit_h_berserker": {"aggression": 85, "AggressionPenalty": 0.1, "agility": 60, "AgilityPenalty": 0.1, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -300, "cooldown": 13, "cost": 0, "critChance": 0.1, "damage": 9, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 50, "IdleCount": 1, "meleeResist": 0, "Moral": 9999, "MoralDistanceBack": 120, "MoralDistanceForward": 120, "moveSpeed": 16, "Noise": 1, "PlayerRank": 1, "poisonResist": 0, "presentationOrderId": 13, "priorityTargets": "z_witch z_dog z_hunter", "push": 0, "pushResist": 0, "rageCost": 13, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 80}, "unit_h_bill": {"aggression": 90, "AggressionPenalty": 0, "agility": 60, "AgilityPenalty": 0, "ammoOnReload": 1, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 0, "AoeX": 18, "BDMoral": 0, "bulletDamage": 13, "BulletMaxDist": 1000, "BulletSpawnOffsetX": 32, "bulletSpeed": 1000, "charges": 1, "cooldown": 1.1, "cost": 0, "critChance": 0, "damage": 2, "DamageToBase": 0, "explosionResist": 0, "fireResist": 1, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 27, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 0, "MoralDistanceForward": 0, "moveSpeed": 0, "needAmmoToShoot": 1, "Noise": 1, "poisonResist": 0, "presentationOrderId": 1, "push": 0, "pushResist": 1, "rageCost": 15, "rangeRadius": 165, "rangeResist": 0, "ShootAngle": 0, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 170, "WeaponName": "Rifle", "WeaponNoise": 30}, "unit_h_builder": {"aggression": 90, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 8, "cooldown": 13, "cost": 25, "critChance": 0.1, "damage": 24, "DamageToBase": 20, "explosionResist": 0, "fireResist": 0, "healthPoints": 54, "IdleCount": 2, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 10, "Noise": 2, "PlayerRank": 2, "poisonResist": 0, "presentationOrderId": 21, "priorityTargets": "z_witch z_dog z_hunter", "push": 15, "pushResist": 0.5, "rageCost": 0, "rangeResist": 0.9, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 90, "visRadius": 80}, "unit_h_carlos": {"aggression": 100, "AggressionPenalty": 0.5, "agility": 100, "AgilityPenalty": 0.5, "ammoOnReload": 30, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "bulletDamage": 3, "BulletMaxDist": 350, "BulletSpawnOffsetX": 28, "bulletSpeed": 1000, "charges": 1, "cooldown": 45, "cost": 20, "critChance": 0, "damage": 2, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 22, "IdleCount": 4, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 8, "needAmmoToShoot": 5, "Noise": 1, "PlayerRank": 9, "poisonResist": 0, "presentationOrderId": 91, "priorityTargets": "z_boomer z_witch z_dog z_hunter", "push": 0, "pushResist": 0, "rageCost": 0, "rangeRadius": 130, "rangeResist": 0, "ShootAngle": 35, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 4500, "visRadius": 200, "WeaponName": "<PERSON><PERSON>", "WeaponNoise": 35}, "unit_h_carol": {"aggression": 90, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "cooldown": 20, "cost": 20, "critChance": 0, "damage": 9, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "healthPoints": 27, "IdleCount": 4, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 10, "Noise": 1, "PlayerRank": 10, "poisonResist": 0, "presentationOrderId": 133, "priorityTargets": "z_boomer z_witch z_dog z_hunter", "push": 0, "pushResist": 0, "rageCost": 0, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 7500, "visRadius": 80}, "unit_h_charlotte": {"aggression": 100, "AggressionPenalty": 0.3, "agility": 90, "AgilityPenalty": 0.5, "ammoOnReload": 6, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 0, "AoeX": 18, "BDMoral": 4, "bulletDamage": 46, "BulletMaxDist": 350, "BulletSpawnOffsetX": 26, "bulletSpeed": 1000, "charges": 1, "cooldown": 35, "cost": 30, "critChance": 0, "damage": 2, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 18, "IdleCount": 4, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 12, "needAmmoToShoot": 1, "Noise": 1, "PlayerRank": 5, "poisonResist": 0, "presentationOrderId": 52, "priorityTargets": "z_boomer z_witch z_dog z_hunter z_insect", "push": 0, "pushResist": 0, "rageCost": 0, "rangeRadius": 180, "rangeResist": 0, "ShootAngle": 10, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 850, "visRadius": 200, "WeaponName": "Pistol", "WeaponNoise": 30}, "unit_h_cheechmarin": {"aggression": 85, "AggressionPenalty": 0.1, "agility": 60, "AgilityPenalty": 0.1, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 16, "cooldown": 4, "cost": 15, "critChance": 0.15, "damage": 11, "DamageToBase": 15, "explosionResist": 0, "fireResist": 0, "healthPoints": 27, "IdleCount": 1, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 120, "MoralDistanceForward": 120, "moveSpeed": 16, "Noise": 1, "PlayerRank": 8, "poisonResist": 0, "presentationOrderId": 81, "priorityTargets": "z_witch z_dog z_hunter", "push": 15, "pushResist": 0, "rageCost": 0, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 2100, "visRadius": 80}, "unit_h_chopper": {"aggression": 100, "AggressionPenalty": 0.5, "agility": 80, "AgilityPenalty": 0.5, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 12, "AoeX": 18, "BDMoral": 4, "cooldown": 60, "cost": 30, "critChance": 0.3, "damage": 24, "DamageToBase": 30, "explosionResist": 0, "fireResist": 0, "healthPoints": 120, "IdleCount": 2, "meleeResist": 0, "Moral": 600, "MoralDistanceBack": 120, "MoralDistanceForward": 120, "moveSpeed": 7, "Noise": 3, "PlayerRank": 4, "poisonResist": 0, "presentationOrderId": 41, "priorityTargets": "z_witch z_dog z_hunter", "push": "15 30", "pushResist": 1, "rageCost": 0, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 350, "visRadius": 80}, "unit_h_cop": {"aggression": 100, "AggressionPenalty": 0.5, "agility": 100, "AgilityPenalty": 0.5, "ammoOnReload": 4, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "bulletDamage": 10, "BulletMaxDist": 350, "BulletSpawnOffsetX": 18, "bulletSpeed": 800, "charges": 4, "cooldown": 35, "cost": 25, "critChance": 0, "damage": 2, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 60, "IdleCount": 4, "meleeResist": 0.5, "Moral": 600, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 6, "needAmmoToShoot": 1, "Noise": 1, "PlayerRank": 6, "poisonResist": 0, "presentationOrderId": 62, "priorityTargets": "z_boomer z_witch z_dog z_hunter", "push": 0, "pushResist": 1, "rageCost": 0, "rangeRadius": 115, "rangeResist": 0, "ShootAngle": 35, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 1250, "visRadius": 200, "WeaponName": "Spas", "WeaponNoise": 26}, "unit_h_farmer": {"aggression": 100, "AggressionPenalty": 0.5, "agility": 90, "AgilityPenalty": 0.5, "ammoOnReload": 2, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "bulletDamage": 6, "BulletMaxDist": 350, "BulletSpawnOffsetX": 18, "bulletSpeed": 800, "charges": 5, "cooldown": 30, "cost": 20, "critChance": 0, "damage": 2, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 18, "IdleCount": 4, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 6, "needAmmoToShoot": 1, "Noise": 1, "PlayerRank": 2, "poisonResist": 0, "presentationOrderId": 20, "priorityTargets": "z_boomer z_witch z_dog z_hunter", "push": 0, "pushResist": 0, "rageCost": 0, "rangeRadius": 105, "rangeResist": 0, "ShootAngle": 35, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 18, "visRadius": 200, "WeaponName": "Shotgun", "WeaponNoise": 28}, "unit_h_firefighter": {"aggression": 95, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 8, "cooldown": 25, "cost": 25, "critChance": 0.2, "damage": 27, "DamageToBase": 20, "explosionResist": 1, "fireResist": 1, "healthPoints": 60, "IdleCount": 1, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 10, "Noise": 2, "PlayerRank": 5, "poisonResist": 1, "presentationOrderId": 51, "priorityTargets": "z_witch z_dog z_hunter z_boomer ", "push": 0, "pushResist": 0.5, "rageCost": 0, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 480, "visRadius": 80}, "unit_h_flamethrower": {"aggression": 100, "AggressionPenalty": 0.5, "agility": 100, "AgilityPenalty": 0.5, "ammoOnReload": 15, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 8, "bulletDamage": 2, "BulletMaxDist": 85, "BulletSpawnOffsetX": 18, "bulletSpeed": 100, "charges": 3, "cooldown": 25, "cost": 30, "critChance": 0, "damage": 10, "DamageToBase": 20, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 12, "IdleCount": 2, "meleeResist": 0, "Moral": 400, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 5, "needAmmoToShoot": 1, "Noise": 2, "PlayerRank": 1, "poisonResist": 1, "presentationOrderId": 101, "priorityTargets": "z_witch z_dog z_hunter", "push": 0, "pushResist": 0.5, "rageCost": 0, "rangeRadius": 78, "rangeResist": 0.9, "ShootAngle": 40, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 200, "WeaponName": "Flamethrower", "WeaponNoise": 40}, "unit_h_glenn": {"aggression": 85, "AggressionPenalty": 0.1, "agility": 60, "AgilityPenalty": 0.1, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 16, "cooldown": 3, "cost": 25, "critChance": 0.1, "damage": 13, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 30, "IdleCount": 1, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 120, "MoralDistanceForward": 120, "moveSpeed": 16, "Noise": 1, "PlayerRank": 6, "poisonResist": 0, "presentationOrderId": 61, "priorityTargets": "z_witch z_dog z_hunter", "push": 0, "pushResist": 0.2, "rageCost": 0, "rangeResist": 0, "spawnShift": -28, "specialActionProbability": 0, "unlockCoins": 700, "visRadius": 80}, "unit_h_grenader": {"aggression": 85, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "cooldown": 20, "cost": 25, "critChance": 0, "damage": 27, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "healthPoints": 54, "IdleCount": 1, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 10, "Noise": 1, "PlayerRank": 7, "poisonResist": 0, "presentationOrderId": 71, "priorityTargets": "z_witch z_dog z_hunter", "push": 0, "pushResist": 0.8, "rageCost": 0, "rangeResist": 0.9, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 1150, "visRadius": 400}, "unit_h_guard": {"aggression": 90, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 8, "cooldown": 13, "cost": 30, "critChance": 0.3, "damage": 25, "DamageToBase": 20, "explosionResist": 0, "fireResist": 0, "healthPoints": 70, "IdleCount": 1, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 14, "Noise": 2, "PlayerRank": 9, "poisonResist": 0, "presentationOrderId": 92, "priorityTargets": "z_witch z_dog z_hunter", "push": 15, "pushResist": 0.6, "rageCost": 0, "rangeResist": 0.9, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 7000, "visRadius": 80}, "unit_h_heavyGuard": {"aggression": 100, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "ammoOnReload": 3, "antiFireResist": 0, "antiRangeResist": 1, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "bulletDamage": 2, "BulletMaxDist": 75, "BulletSpawnOffsetX": 18, "bulletSpeed": 800, "charges": 1, "cooldown": 13, "cost": 40, "critChance": 0, "damage": 10, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 50, "IdleCount": 1, "meleeResist": 0, "Moral": 600, "MoralDistanceBack": 120, "MoralDistanceForward": 120, "moveSpeed": 6, "needAmmoToShoot": 1, "Noise": 2, "PlayerRank": 12, "poisonResist": 0, "presentationOrderId": 121, "priorityTargets": "z_witch z_dog z_hunter", "push": 0, "pushResist": 1, "rageCost": 0, "rangeFreezing": 1000, "rangeRadius": 18, "rangeResist": 0.9, "shieldPoints": 20, "ShootAngle": 25, "spawnShift": -20, "specialActionProbability": 0, "unlockCoins": 10000, "visRadius": 170, "WeaponName": "Taser", "WeaponNoise": 30}, "unit_h_hero": {"aggression": 100, "AggressionPenalty": 0.5, "agility": 100, "AgilityPenalty": 0.5, "ammoOnReload": 4, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "bulletDamage": 10, "BulletMaxDist": 350, "BulletSpawnOffsetX": 18, "bulletSpeed": 800, "charges": 4, "cooldown": 35, "cost": 30, "critChance": 0, "damage": 2, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 24, "IdleCount": 4, "meleeResist": 0, "Moral": 600, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 11, "needAmmoToShoot": 1, "Noise": 1, "PlayerRank": 12, "poisonResist": 0, "presentationOrderId": 120, "priorityTargets": "z_boomer z_witch z_dog z_hunter", "push": 0, "pushResist": 0.7, "rageCost": 0, "rangeRadius": 115, "rangeResist": 0.9, "ShootAngle": 35, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 15000, "visRadius": 200, "WeaponName": "Spas", "WeaponNoise": 26}, "unit_h_jailer": {"aggression": 90, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 8, "cooldown": 13, "cost": 15, "critChance": 0.2, "damage": 12, "DamageToBase": 20, "explosionResist": 0, "fireResist": 0, "healthPoints": 41, "IdleCount": 2, "meleeFreezing": 1500, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 15, "Noise": 2, "PlayerRank": 1, "poisonResist": 0, "presentationOrderId": 11, "priorityTargets": "z_witch z_dog z_hunter", "push": 0, "pushResist": 0, "rageCost": 0, "rangeFreezing": 1500, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "UpgradeCategory": "Golden", "visRadius": 80}, "unit_h_jailerrifle": {"aggression": 100, "AggressionPenalty": 0.5, "agility": 90, "AgilityPenalty": 0.5, "ammoOnReload": 20, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "bulletDamage": 3, "BulletMaxDist": 350, "BulletSpawnOffsetX": 18, "bulletSpeed": 800, "charges": 1, "cooldown": 45, "cost": 20, "critChance": 0, "damage": 2, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 18, "IdleCount": 4, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 6, "needAmmoToShoot": 1, "Noise": 1, "PlayerRank": 7, "poisonResist": 0, "presentationOrderId": 72, "priorityTargets": "z_boomer z_witch z_dog z_hunter", "push": 0, "pushResist": 0.2, "rageCost": 0, "rangeRadius": 145, "rangeResist": 0, "ShootAngle": 25, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 1750, "visRadius": 200, "WeaponName": "Mp5", "WeaponNoise": 30}, "unit_h_judi": {"aggression": 100, "AggressionPenalty": 0.3, "agility": 90, "AgilityPenalty": 0.5, "ammoOnReload": 10, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 0, "AoeX": 18, "BDMoral": 40, "bulletDamage": 4, "BulletMaxDist": 350, "BulletSpawnOffsetX": 32, "bulletSpeed": 500, "charges": 1, "cooldown": 35, "cost": 25, "critChance": 0, "damage": 5, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 22, "IdleCount": 4, "meleeResist": 0, "Moral": 400, "MoralDistanceBack": 120, "MoralDistanceForward": 120, "moveSpeed": 12, "needAmmoToShoot": 1, "Noise": 1, "PlayerRank": 3, "poisonResist": 0, "presentationOrderId": 31, "priorityTargets": "z_boomer z_witch z_dog z_hunter", "push": "15 15", "pushResist": 0, "rageCost": 0, "rangeRadius": 120, "rangeResist": 0, "ShootAngle": 20, "spawnShift": 0, "specialActionProbability": 100, "unlockCoins": 150, "visRadius": 150, "WeaponName": "Pistol", "WeaponNoise": 30}, "unit_h_lester": {"aggression": 100, "AggressionPenalty": 0.5, "agility": 90, "AgilityPenalty": 0.5, "ammoOnReload": 2, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "bulletDamage": 6, "BulletMaxDist": 350, "BulletSpawnOffsetX": 18, "bulletSpeed": 800, "charges": 5, "cooldown": 30, "cost": 20, "critChance": 0, "damage": 2, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 18, "IdleCount": 4, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 6, "needAmmoToShoot": 1, "Noise": 1, "PlayerRank": 1, "poisonResist": 0, "presentationOrderId": 20, "priorityTargets": "z_boomer z_witch z_dog z_hunter", "push": 0, "pushResist": 0, "rageCost": 0, "rangeRadius": 105, "rangeResist": 0, "ShootAngle": 35, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 200, "WeaponName": "Shotgun", "WeaponNoise": 28}, "unit_h_lightSoldier": {"aggression": 90, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "cooldown": 14, "cost": 30, "critChance": 0.1, "damage": 32, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "healthPoints": 72, "IdleCount": 1, "meleeResist": 0, "Moral": 600, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 10, "Noise": 1, "PlayerRank": 13, "poisonResist": 1, "presentationOrderId": 132, "priorityTargets": "z_boomer z_witch z_dog z_hunter", "push": 0, "pushResist": 0.7, "rageCost": 0, "rangeResist": 0.9, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 24000, "visRadius": 200}, "unit_h_mechanic": {"aggression": 85, "AggressionPenalty": 0.1, "agility": 60, "AgilityPenalty": 0.1, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 16, "cooldown": 20, "cost": 15, "critChance": 0, "damage": 9, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 27, "IdleCount": 1, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 120, "MoralDistanceForward": 120, "moveSpeed": 16, "Noise": 1, "PlayerRank": 3, "poisonResist": 0, "presentationOrderId": 30, "priorityTargets": "z_witch z_dog z_hunter z_<PERSON><PERSON>un", "push": 30, "pushResist": 0, "rageCost": 0, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 200, "visRadius": 100}, "unit_h_medic": {"aggression": 100, "AggressionPenalty": 0.3, "agility": 90, "AgilityPenalty": 0.5, "ammoOnReload": 10, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 0, "AoeX": 18, "BDMoral": 40, "bulletDamage": 4, "BulletMaxDist": 350, "BulletSpawnOffsetX": 32, "bulletSpeed": 1000, "charges": 1, "cooldown": 45, "cost": 35, "critChance": 0, "damage": 6, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 22, "IdleCount": 4, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 120, "MoralDistanceForward": 120, "moveSpeed": 12, "needAmmoToShoot": 1, "Noise": 1, "PlayerRank": 10, "poisonResist": 1, "presentationOrderId": 100, "priorityTargets": "z_boomer z_witch z_dog z_hunter", "push": 0, "pushResist": 0, "rageCost": 0, "rangeRadius": 75, "rangeResist": 0.9, "ShootAngle": 20, "spawnShift": 0, "specialActionProbability": 100, "unlockCoins": 11000, "visRadius": 150, "WeaponName": "Pistol", "WeaponNoise": 30}, "unit_h_naked": {"aggression": 85, "AggressionPenalty": 0.1, "agility": 60, "AgilityPenalty": 0.1, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 16, "cooldown": 3, "cost": 15, "critChance": 0.1, "damage": 9, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 27, "IdleCount": 1, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 120, "MoralDistanceForward": 120, "moveSpeed": 16, "Noise": 1, "PlayerRank": 1, "poisonResist": 0, "presentationOrderId": 10, "priorityTargets": "z_witch z_dog z_hunter", "push": 0, "pushResist": 0, "rageCost": 0, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 0, "visRadius": 80}, "unit_h_puke": {"aggression": 90, "AggressionPenalty": 0.1, "agility": 60, "AgilityPenalty": 0.1, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 16, "cooldown": 3, "cost": 20, "critChance": 0.2, "damage": 9, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 41, "IdleCount": 1, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 120, "MoralDistanceForward": 120, "moveSpeed": 14, "Noise": 1, "poisonResist": 0, "presentationOrderId": 14, "priorityTargets": "z_witch z_dog z_hunter", "push": 0, "pushResist": 0.2, "rageCost": 0, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 80}, "unit_h_queen": {"aggression": 85, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -300, "cooldown": 35, "cost": 0, "critChance": 0, "damage": 9, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "healthPoints": 22, "IdleCount": 1, "meleeResist": 0, "Moral": 9999, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 10, "Noise": 1, "PlayerRank": 1, "poisonResist": 0, "presentationOrderId": 135, "priorityTargets": "z_witch z_dog z_hunter", "push": 0, "pushResist": 0, "rageCost": 25, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 400}, "unit_h_saw": {"aggression": 90, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 23, "BDMoral": -300, "cooldown": 13, "cost": 0, "critChance": 0.7, "damage": 10, "DamageToBase": 30, "explosionResist": 0, "fireResist": 0, "healthPoints": 54, "IdleCount": 2, "meleeResist": 0, "Moral": 9999, "MoralDistanceBack": 100, "MoralDistanceForward": 100, "moveSpeed": 13, "Noise": 3, "PlayerRank": 1, "poisonResist": 0, "presentationOrderId": 12, "priorityTargets": "z_witch z_dog z_hunter", "push": 0, "pushResist": 0.2, "rageCost": 13, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 80}, "unit_h_sniper": {"aggression": 90, "AggressionPenalty": 0.5, "agility": 90, "AgilityPenalty": 0.5, "ammoOnReload": 5, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "bulletDamage": 50, "BulletMaxDist": 350, "BulletSpawnOffsetX": 26, "bulletSpeed": 1000, "charges": 1, "cooldown": 50, "cost": 20, "critChance": 0, "damage": 2, "DamageToBase": 0, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 18, "IdleCount": 1, "meleeResist": 0, "Moral": 400, "MoralDistanceBack": 40, "MoralDistanceForward": 40, "moveSpeed": 6, "needAmmoToShoot": 1, "Noise": 1, "PlayerRank": 8, "poisonResist": 0, "presentationOrderId": 82, "priorityTargets": "z_boomer z_witch z_dog z_hunter", "push": 0, "pushResist": 0, "rageCost": 0, "rangeRadius": 250, "rangeResist": 0, "ShootAngle": 10, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 2500, "visRadius": 250, "WeaponName": "Rifle", "WeaponNoise": 40}, "unit_h_soldierCap": {"aggression": 100, "AggressionPenalty": 0.3, "agility": 90, "AgilityPenalty": 0.5, "ammoOnReload": 10, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 0, "AoeX": 18, "BDMoral": 40, "bulletDamage": 4, "BulletMaxDist": 350, "BulletSpawnOffsetX": 32, "bulletSpeed": 1000, "charges": 1, "cooldown": 40, "cost": 35, "critChance": 0, "damage": 6, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 22, "IdleCount": 4, "meleeResist": 0, "Moral": 400, "MoralDistanceBack": 120, "MoralDistanceForward": 120, "moveSpeed": 10, "needAmmoToShoot": 1, "Noise": 1, "PlayerRank": 1, "poisonResist": 1, "presentationOrderId": 111, "priorityTargets": "z_boomer z_witch z_dog z_hunter", "push": 0, "pushResist": 0.2, "rageCost": 0, "rangeRadius": 140, "rangeResist": 0.9, "ShootAngle": 20, "spawnShift": 0, "specialActionProbability": 100, "visRadius": 200, "WeaponName": "Pistol", "WeaponNoise": 30}, "unit_h_sonya": {"aggression": 85, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "ammoOnReload": 12, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "bulletDamage": 4, "BulletMaxDist": 250, "BulletSpawnOffsetX": 32, "bulletSpeed": 1000, "charges": 1, "cooldown": 45, "cost": 25, "critChance": 0.4, "damage": 6, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 50, "IdleCount": 1, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 12, "needAmmoToShoot": 1, "Noise": 1, "PlayerRank": 11, "poisonResist": 0, "presentationOrderId": 112, "priorityTargets": "z_witch z_dog z_hunter", "push": 15, "pushResist": 0, "rageCost": 0, "rangeRadius": 135, "rangeResist": 0, "ShootAngle": 40, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 12500, "visRadius": 200, "WeaponName": "<PERSON><PERSON>", "WeaponNoise": 30}, "unit_h_specops": {"aggression": 85, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "ammoOnReload": 12, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "bulletDamage": 4, "BulletMaxDist": 250, "BulletSpawnOffsetX": 20, "bulletSpeed": 1000, "charges": 1, "cooldown": 45, "cost": 30, "critChance": 0.4, "damage": 6, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 50, "IdleCount": 1, "meleeResist": 0, "Moral": 600, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 12, "needAmmoToShoot": 1, "Noise": 1, "PlayerRank": 1, "poisonResist": 1, "presentationOrderId": 11, "priorityTargets": "z_witch z_dog z_hunter", "push": "5 5", "pushResist": 0, "rageCost": 0, "rangeRadius": 140, "rangeResist": 0.9, "ShootAngle": 40, "spawnShift": 0, "specialActionProbability": 0, "UpgradeCategory": "Golden", "visRadius": 200, "WeaponName": "AK47", "WeaponNoise": 30}, "unit_h_swat": {"aggression": 85, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "ammoOnReload": 12, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "bulletDamage": 4, "BulletMaxDist": 250, "BulletSpawnOffsetX": 20, "bulletSpeed": 1000, "charges": 1, "cooldown": 50, "cost": 30, "critChance": 0.4, "damage": 6, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 35, "IdleCount": 1, "meleeResist": 0, "Moral": 600, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 12, "needAmmoToShoot": 1, "Noise": 1, "PlayerRank": 1, "poisonResist": 1, "presentationOrderId": 131, "priorityTargets": "z_witch z_dog z_hunter", "push": 0, "pushResist": 0, "rageCost": 0, "rangeRadius": 160, "rangeResist": 0.9, "ShootAngle": 40, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 200, "WeaponName": "Mp5", "WeaponNoise": 30}, "unit_h_toadstool": {"aggression": 90, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "cooldown": 90, "cost": 20, "critChance": 0, "damage": 20, "DamageToBase": 20, "explosionResist": 0, "fireResist": 1, "healthPoints": 8, "IdleCount": 4, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 15, "Noise": 2, "PlayerRank": 9, "poisonResist": 0, "presentationOrderId": 90, "priorityTargets": "z_base", "push": 0, "pushResist": 0, "rageCost": 0, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 200}, "unit_h_welder": {"aggression": 90, "AggressionPenalty": 0.5, "agility": 60, "AgilityPenalty": 0.5, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": 4, "cooldown": 90, "cost": 20, "critChance": 0, "damage": 20, "DamageToBase": 20, "explosionResist": 0, "fireResist": 1, "healthPoints": 8, "IdleCount": 4, "meleeResist": 0, "Moral": 200, "MoralDistanceBack": 80, "MoralDistanceForward": 80, "moveSpeed": 20, "Noise": 2, "PlayerRank": 9, "poisonResist": 0, "presentationOrderId": 90, "priorityTargets": "z_base", "push": 0, "pushResist": 0, "rageCost": 0, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 3200, "visRadius": 200}, "unit_item_barrel": {"aggression": 1, "AggressionPenalty": 0, "agility": 1, "AgilityPenalty": 0, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 999, "AoeX": 999, "BDMoral": 0, "cooldown": 10, "cost": 0, "critChance": 0, "damage": 40, "DamageToBase": 2, "explosionResist": 0, "fireResist": 1, "healthPoints": 30, "IdleCount": 0, "meleeResist": 0, "Moral": 4, "MoralDistanceBack": 0, "MoralDistanceForward": 0, "moveSpeed": 0, "PlayerRank": 2, "poisonResist": 0, "presentationOrderId": 1, "push": 0, "pushResist": 0, "rageCost": 6, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 0}, "unit_item_bomb": {"aggression": 1, "AggressionPenalty": 0, "agility": 1, "AgilityPenalty": 0, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 999, "AoeX": 999, "BDMoral": 0, "cooldown": 60, "cost": 0, "critChance": 0, "damage": 120, "DamageToBase": 2, "explosionResist": 0, "fireResist": 1, "healthPoints": 60, "IdleCount": 0, "meleeResist": 0, "Moral": 4, "MoralDistanceBack": 0, "MoralDistanceForward": 0, "moveSpeed": 0, "poisonResist": 0, "presentationOrderId": 999, "push": 0, "pushResist": 0, "rageCost": 25, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_item_generator": {"aggression": 1, "AggressionPenalty": 0, "agility": 1, "AgilityPenalty": 0, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 999, "AoeX": 999, "BDMoral": 0, "cooldown": 40, "cost": 0, "critChance": 0, "damage": 0, "DamageToBase": 2, "explosionResist": 0, "fireResist": 1, "healthPoints": 105, "IdleCount": 0, "meleeResist": 0, "Moral": 4, "MoralDistanceBack": 0, "MoralDistanceForward": 0, "moveSpeed": 0, "PlayerRank": 7, "poisonResist": 0, "presentationOrderId": 70, "push": 0, "pushResist": 0, "rageCost": 20, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 2000}, "unit_item_medkit": {"aggression": 1, "AggressionPenalty": 0, "agility": 1, "AgilityPenalty": 0, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 25, "AoeX": 38, "BDMoral": 0, "cooldown": 30, "cost": 0, "critChance": 0, "damage": 0, "DamageToBase": 2, "explosionResist": 0, "fireResist": 1, "healthPoints": 0, "IdleCount": 0, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 0, "MoralDistanceForward": 0, "moveSpeed": 0, "PlayerRank": 4, "poisonResist": 0, "presentationOrderId": 40, "push": 0, "pushResist": 0, "rageCost": 9, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 330}, "unit_item_molotov": {"aggression": 1, "AggressionPenalty": 0, "agility": 1, "AgilityPenalty": 0, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 25, "AoeX": 35, "BDMoral": 0, "cooldown": 30, "cost": 0, "critChance": 0, "damage": 5, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "healthPoints": 60, "IdleCount": 0, "meleeResist": 0, "Moral": 4, "MoralDistanceBack": 0, "MoralDistanceForward": 0, "moveSpeed": 0, "PlayerRank": 6, "poisonResist": 0, "presentationOrderId": 60, "push": 0, "pushResist": 0, "rageCost": 15, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 1150}, "unit_item_nitrogen": {"aggression": 1, "AggressionPenalty": 0, "agility": 1, "AgilityPenalty": 0, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 50, "AoeX": 50, "BDMoral": 0, "cooldown": 60, "cost": 0, "critChance": 0, "damage": 5, "DamageToBase": 2, "explosionResist": 0, "fireResist": 1, "healthPoints": 60, "IdleCount": 0, "meleeResist": 0, "Moral": 4, "MoralDistanceBack": 0, "MoralDistanceForward": 0, "moveSpeed": 0, "PlayerRank": 8, "poisonResist": 0, "presentationOrderId": 80, "push": 0, "pushResist": 0, "rageCost": 15, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 3200}, "unit_item_red_barrel": {"aggression": 1, "AggressionPenalty": 0, "agility": 1, "AgilityPenalty": 0, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 12, "AoeX": 18, "BDMoral": 0, "cooldown": 10, "cost": 0, "critChance": 0, "damage": 18, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "healthPoints": 48, "IdleCount": 0, "meleeResist": 0, "Moral": 4, "MoralDistanceBack": 0, "MoralDistanceForward": 0, "moveSpeed": 0, "Noise": 1, "PlayerRank": 5, "poisonResist": 0, "presentationOrderId": 50, "push": 0, "pushResist": 0, "rageCost": 9, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 700}, "unit_item_turret": {"aggression": 1, "AggressionPenalty": 0, "agility": 1, "AgilityPenalty": 0, "ammoOnReload": 80, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 50, "AoeX": 50, "BDMoral": 0, "bulletDamage": 5, "BulletMaxDist": 350, "BulletSpawnOffsetX": 15, "bulletSpeed": 1000, "charges": 1, "cooldown": 45, "cost": 0, "critChance": 0, "damage": 5, "DamageToBase": 2, "explosionResist": 0, "fireResist": 1, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 60, "IdleCount": 0, "meleeResist": 0, "Moral": 4, "MoralDistanceBack": 0, "MoralDistanceForward": 0, "moveSpeed": 0, "needAmmoToShoot": 1, "PlayerRank": 11, "poisonResist": 0, "presentationOrderId": 110, "push": 0, "pushResist": 0, "rageCost": 25, "rangeRadius": 150, "rangeResist": 0, "ShootAngle": 30, "spawnShift": 0, "specialActionProbability": 0, "unlockCoins": 18000, "visRadius": 200, "WeaponName": "<PERSON><PERSON><PERSON>", "WeaponNoise": 30}, "unit_z_alienCop": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -32, "cooldown": 0, "cost": 0, "critChance": 0.5, "damage": 15, "DamageToBase": 10, "explosionResist": 0, "fireResist": 1, "healthPoints": 1200, "IdleCount": 1, "meleeFreezing": 1500, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 52, "push": 0, "pushResist": 1, "rageCost": 10, "rangeFreezing": 1500, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 50}, "unit_z_alienNaked": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0.3, "damage": 25, "DamageToBase": 10, "explosionResist": 0, "fireResist": 1, "healthPoints": 150, "IdleCount": 1, "meleeFreezing": 1500, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 50, "push": 0, "pushResist": 0, "rageCost": 1, "rangeFreezing": 1500, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_alienNakedRun": {"aggression": 18, "AggressionPenalty": 0.8, "agility": 80, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0.3, "damage": 25, "DamageToBase": 10, "explosionResist": 0, "fireResist": 1, "healthPoints": 150, "IdleCount": 1, "meleeFreezing": 1500, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 51, "push": 0, "pushResist": 0, "rageCost": 1, "rangeFreezing": 1500, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_blackSkeleton": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -24, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 9, "DamageToBase": 5, "explosionResist": 0, "fireResist": 0, "healthPoints": 53, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 31, "push": 0, "pushResist": 0, "rageCost": 1, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_boomer": {"aggression": 18, "AggressionPenalty": 0.8, "agility": 35, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -96, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 46, "DamageToBase": 2, "explosionResist": 0, "fireResist": 0, "healthPoints": 7, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 100, "moveSpeed": 2, "poisonResist": 0, "presentationOrderId": 16, "push": 0, "pushResist": 1, "rageCost": 2, "rangeResist": 0, "spawnShift": 20, "specialActionProbability": 0}, "unit_z_boss": {"aggression": 25, "AggressionPenalty": 0.9, "agility": 100, "AgilityPenalty": 0.9, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -112, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 999, "DamageToBase": 6, "explosionResist": 0, "fireResist": 1, "healthPoints": 550, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 17, "push": 0, "pushResist": 1, "rageCost": 50, "rangeResist": 0, "spawnShift": 20, "specialActionProbability": 0}, "unit_z_builder": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 78, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 8, "push": 0, "pushResist": 0.5, "rageCost": 3, "rangeResist": 0.9, "spawnShift": -13, "specialActionProbability": 0}, "unit_z_builderRun": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 78, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 9, "push": 25, "pushResist": 0.5, "rageCost": 3, "rangeResist": 0.9, "spawnShift": -13, "specialActionProbability": 0}, "unit_z_bulletproof": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 9, "DamageToBase": 8, "explosionResist": 0, "fireResist": 0, "healthPoints": 130, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 24, "push": 0, "pushResist": 0.5, "rageCost": 3, "rangeResist": 0.9, "spawnShift": -13, "specialActionProbability": 0}, "unit_z_cephalopoda": {"aggression": 25, "AggressionPenalty": 0.9, "agility": 100, "AgilityPenalty": 0.9, "antiFireResist": 1, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 1, "BDMoral": -112, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 999, "DamageToBase": 6, "explosionResist": 0, "fireResist": 1, "healthPoints": 8000, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 61, "push": 0, "pushResist": 1, "rageCost": 50, "rangeResist": 0.9, "spawnShift": 20, "specialActionProbability": 0}, "unit_z_cheechmarin": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 14, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 55, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 39, "push": 0, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": -21, "specialActionProbability": 0}, "unit_z_chinese": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0.3, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 42, "push": "15 0", "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": -21, "specialActionProbability": 0}, "unit_z_chineseExpl": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0.3, "damage": 20, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 101, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 43, "push": "15 0", "pushResist": 0.2, "rageCost": 2, "rangeResist": 0, "spawnShift": -21, "specialActionProbability": 0}, "unit_z_chopper": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 23, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 1, "damage": 7, "DamageToBase": 15, "explosionResist": 0, "fireResist": 0, "healthPoints": 600, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 15, "push": "15 15", "pushResist": 1, "rageCost": 6, "rangeResist": 0, "spawnShift": -13, "specialActionProbability": 0}, "unit_z_cop": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 5, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0.1, "healthPoints": 260, "IdleCount": 1, "meleeResist": 0.4, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 13, "push": 0, "pushResist": 1, "rageCost": 10, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_copsmall": {"aggression": 18, "AggressionPenalty": 0.8, "agility": 40, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0.3, "damage": 24, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 78, "IdleCount": 1, "meleeResist": 0.9, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 34, "push": 0, "pushResist": 0.35, "rageCost": 3, "rangeResist": 0, "spawnShift": -13, "specialActionProbability": 0}, "unit_z_crank": {"aggression": 8, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 28, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 12, "DamageToBase": 6, "explosionResist": 0, "fireResist": 0, "healthPoints": 1400, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 20, "poisonResist": 0, "presentationOrderId": 57, "push": "5 0", "pushResist": 1, "rageCost": 5, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_demon": {"aggression": 50, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 8, "DamageToBase": 25, "explosionResist": 0, "fireResist": 0, "healthPoints": 800, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 37, "push": 0, "pushResist": 1, "rageCost": 10, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 50}, "unit_z_dog": {"aggression": 18, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -48, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 13, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 10, "IdleCount": 0, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 40, "poisonResist": 0, "presentationOrderId": 7, "push": 0, "pushResist": 0, "rageCost": 1, "rangeResist": 0, "spawnShift": -20, "specialActionProbability": 0}, "unit_z_egg": {"aggression": 100, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 0, "DamageToBase": 0, "explosionResist": 0, "fireResist": 1, "healthPoints": 2400, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 0, "poisonResist": 0, "presentationOrderId": 54, "push": 0, "pushResist": 1, "rageCost": 10, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_firefighter": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 15, "DamageToBase": 10, "explosionResist": 0, "fireResist": 1, "healthPoints": 70, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 6, "poisonResist": 1, "presentationOrderId": 36, "push": 0, "pushResist": 0.5, "rageCost": 2, "rangeResist": 0, "spawnShift": -11, "specialActionProbability": 0}, "unit_z_freehugs": {"aggression": 100, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 0, "DamageToBase": 0, "explosionResist": 0, "fireResist": 0, "healthPoints": 10, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 0, "poisonResist": 0, "presentationOrderId": 40, "push": 0, "pushResist": 0, "rageCost": 50, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_girl": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0.5, "damage": 10, "DamageToBase": 5, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 10, "push": 15, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_heavyGuard": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 78, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 8, "push": 0, "pushResist": 0.5, "rageCost": 3, "rangeResist": 0.9, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_hunter": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 5, "DamageToBase": 5, "explosionResist": 0, "fireResist": 0, "healthPoints": 45, "IdleCount": 0, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 11, "push": 0, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": -20, "specialActionProbability": 0}, "unit_z_insect": {"aggression": 80, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 23, "BDMoral": -32, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 35, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 140, "IdleCount": 3, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 30, "poisonResist": 0, "presentationOrderId": 53, "push": 0, "pushResist": 0, "rageCost": 4, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 100}, "unit_z_kamikaze": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 40, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 50, "AoeX": 23, "BDMoral": -48, "cooldown": 0, "cost": 0, "critChance": 1, "damage": 45, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 10, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 5, "poisonResist": 0, "presentationOrderId": 29, "push": 0, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": -20, "specialActionProbability": 0}, "unit_z_megavolt": {"aggression": 80, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -112, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 20, "explosionResist": 0, "fireResist": 0, "healthPoints": 820, "IdleCount": 3, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 5, "push": 0, "pushResist": 0.8, "rageCost": 50, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 30}, "unit_z_monk": {"aggression": 50, "AggressionPenalty": 0.8, "agility": 85, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 5, "DamageToBase": 5, "explosionResist": 0, "fireResist": 0, "healthPoints": 42, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 44, "push": "15 15", "pushResist": 0.3, "rageCost": 1, "rangeResist": 0, "spawnShift": -62, "specialActionProbability": 0}, "unit_z_naked": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 60, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 1, "push": 0, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": -21, "specialActionProbability": 5}, "unit_z_nakedRun": {"aggression": 18, "AggressionPenalty": 0.8, "agility": 80, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -32, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 2, "push": 0, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_necromancer": {"aggression": 100, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 0, "DamageToBase": 0, "explosionResist": 0, "fireResist": 0, "healthPoints": 100, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 1, "poisonResist": 0, "presentationOrderId": 38, "push": 0, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 100}, "unit_z_offal": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 40, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 80, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 49, "push": 0, "pushResist": 0, "rageCost": 1, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_paramedic": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 20, "DamageToBase": 10, "explosionResist": 0, "fireResist": 1, "healthPoints": 140, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 5, "poisonResist": 0, "presentationOrderId": 6, "push": 0, "pushResist": 0.2, "rageCost": 2, "rangeResist": 0, "spawnShift": -22, "specialActionProbability": 0}, "unit_z_preAlien": {"aggression": 50, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -24, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 20, "DamageToBase": 5, "explosionResist": 0, "fireResist": 1, "healthPoints": 50, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 100, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 46, "push": "15 15", "pushResist": 0.6, "rageCost": 1, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_preEgg": {"aggression": 50, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 0, "explosionResist": 0, "fireResist": 1, "healthPoints": 1800, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 5, "poisonResist": 0, "presentationOrderId": 55, "push": 0, "pushResist": 0, "rageCost": 10, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 20}, "unit_z_prisoner": {"aggression": 50, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 40, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 350, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 8, "poisonResist": 0, "presentationOrderId": 60, "push": 0, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": -21, "specialActionProbability": 5}, "unit_z_prisonerLocusts": {"aggression": 100, "AggressionPenalty": 0.8, "agility": 90, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 20, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 200, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 16, "poison": 5, "poisonResist": 0, "presentationOrderId": 58, "push": 0, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": -21, "specialActionProbability": 5}, "unit_z_psy": {"aggression": 50, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -24, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 15, "DamageToBase": 5, "explosionResist": 0, "fireResist": 1, "healthPoints": 65, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 45, "push": "15 15", "pushResist": 0, "rageCost": 1, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 90}, "unit_z_puke": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 52, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 5, "poisonResist": 0, "presentationOrderId": 4, "push": 0, "pushResist": 0.2, "rageCost": 2, "rangeResist": 0, "spawnShift": -22, "specialActionProbability": 25}, "unit_z_pumpkinNaked": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 40, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 117, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 0, "push": 0, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": -21, "specialActionProbability": 0}, "unit_z_pumpkinNakedRun": {"aggression": 18, "AggressionPenalty": 0.8, "agility": 80, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -32, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 117, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 0, "push": 0, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_putrid": {"aggression": 8, "AggressionPenalty": 0.8, "agility": 40, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 30, "DamageToBase": 15, "explosionResist": 0, "fireResist": 0, "healthPoints": 800, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 47, "push": 0, "pushResist": 1, "rageCost": 4, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_rotten": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -24, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 12, "DamageToBase": 5, "explosionResist": 0, "fireResist": 0, "healthPoints": 65, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 41, "push": "15 0", "pushResist": 0.1, "rageCost": 1, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_shielder": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 40, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 15, "explosionResist": 0, "fireResist": 0, "healthPoints": 80, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 28, "push": 0, "pushResist": 0.2, "rageCost": 4, "rangeResist": 0, "spawnShift": -22, "specialActionProbability": 50}, "unit_z_skeleton": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -24, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 3, "DamageToBase": 5, "explosionResist": 0, "fireResist": 0, "healthPoints": 20, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 3, "push": 0, "pushResist": 0, "rageCost": 1, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_skeletonMillitary": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 80, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 5, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 44, "IdleCount": 0, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 12, "poisonResist": 0, "presentationOrderId": 32, "push": 0, "pushResist": 0.3, "rageCost": 2, "rangeResist": 0.9, "spawnShift": 1, "specialActionProbability": 0}, "unit_z_skeletonSapper": {"aggression": 50, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -24, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 5, "explosionResist": 0, "fireResist": 0.3, "healthPoints": 100, "IdleCount": 1, "meleeResist": 0.7, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 100, "moveSpeed": 5, "poisonResist": 0, "presentationOrderId": 33, "push": 0, "pushResist": 0.9, "rageCost": 1, "rangeResist": 0.95, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_smoker": {"aggression": 13, "AggressionPenalty": 0.5, "agility": 40, "AgilityPenalty": 0.5, "ammoOnReload": 1, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -48, "bulletDamage": 9, "BulletMaxDist": 120, "BulletSpawnOffsetX": 23, "bulletSpeed": 600, "charges": 4, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "friendlyFireChance": 1, "friendlyFireMinDist": 20, "healthPoints": 39, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 4, "needAmmoToShoot": 1, "poisonResist": 0, "presentationOrderId": 30, "push": 0, "pushResist": 0, "rageCost": 2, "rangeRadius": 120, "rangeResist": 0, "ShootAngle": 40, "spawnShift": 0, "specialActionProbability": 50, "visRadius": 170, "WeaponName": "Smoker"}, "unit_z_soldier": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 14, "DamageToBase": 20, "explosionResist": 0, "fireResist": 0, "healthPoints": 55, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 12, "push": 0, "pushResist": 0.4, "rageCost": 1, "rangeResist": 0.3, "spawnShift": -10, "specialActionProbability": 0}, "unit_z_soldierfat": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0.5, "damage": 7, "DamageToBase": 15, "explosionResist": 0, "fireResist": 0.3, "healthPoints": 728, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 25, "push": 20, "pushResist": 1, "rageCost": 6, "rangeResist": 0, "spawnShift": 30, "specialActionProbability": 0}, "unit_z_sphere": {"aggression": 100, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 1, "BDMoral": -48, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 1, "healthPoints": 120, "IdleCount": 3, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 5, "poisonResist": 0, "presentationOrderId": 48, "push": 0, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0, "visRadius": 80}, "unit_z_swat": {"aggression": 80, "AggressionPenalty": 0.8, "agility": 80, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 15, "DamageToBase": 20, "explosionResist": 0, "fireResist": 1, "healthPoints": 1200, "IdleCount": 1, "meleeFreezing": 3000, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 200, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 56, "push": 0, "pushResist": 0.5, "rageCost": 5, "rangeFreezing": 1500, "rangeResist": 0.5, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_twins": {"aggression": 100, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 53, "DamageToBase": 17, "explosionResist": 0, "fireResist": 0, "healthPoints": 500, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 1, "poisonResist": 0, "presentationOrderId": 59, "push": "25 25", "pushResist": 0, "rageCost": 5, "rangeResist": 0.95, "spawnShift": -21, "specialActionProbability": 5}, "unit_z_undead": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 40, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 14, "DamageToBase": 20, "explosionResist": 0, "fireResist": 0, "healthPoints": 55, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 35, "push": 0, "pushResist": 0.4, "rageCost": 1, "rangeResist": 0.3, "spawnShift": -4, "specialActionProbability": 0}, "unit_z_witch": {"aggression": 18, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -48, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 13, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 0, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 30, "poisonResist": 0, "presentationOrderId": 14, "push": 0, "pushResist": 0, "rageCost": 6, "rangeResist": 0, "spawnShift": -20, "specialActionProbability": 0}, "unit_z_xmasCop": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 5, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0.1, "healthPoints": 520, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 0, "push": 0, "pushResist": 1, "rageCost": 10, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_xmasGirl": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0.5, "damage": 10, "DamageToBase": 5, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 0, "push": 15, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": 4, "specialActionProbability": 0}, "unit_z_xmasNaked": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 0, "push": 0, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": -21, "specialActionProbability": 5}, "unit_z_xmasNakedRun": {"aggression": 18, "AggressionPenalty": 0.8, "agility": 80, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -32, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 0, "push": 0, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_yellow": {"aggression": 20, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0.85, "damage": 10, "DamageToBase": 15, "explosionResist": 0, "fireResist": 1, "healthPoints": 78, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 5, "poison": 5, "poisonResist": 0, "presentationOrderId": 26, "push": 0, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": -22, "specialActionProbability": 0}, "unit_z_yellowFat": {"aggression": 8, "AggressionPenalty": 0.8, "agility": 40, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 15, "explosionResist": 0, "fireResist": 1, "healthPoints": 300, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 4, "poisonResist": 0, "presentationOrderId": 27, "push": 0, "pushResist": 0.8, "rageCost": 4, "rangeResist": 0, "spawnShift": -22, "specialActionProbability": 0}, "unit_z_puke_z_farmer": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 52, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 5, "poisonResist": 0, "presentationOrderId": 4, "push": 0, "pushResist": 0.2, "rageCost": 2, "rangeResist": 0, "spawnShift": -22, "specialActionProbability": 25}, "unit_z_puke_z_mechanic": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 52, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 5, "poisonResist": 0, "presentationOrderId": 4, "push": 0, "pushResist": 0.2, "rageCost": 2, "rangeResist": 0, "spawnShift": -22, "specialActionProbability": 25}, "unit_z_girl_z_judi": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0.5, "damage": 10, "DamageToBase": 5, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 10, "push": 15, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_girl_z_charlotte": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0.5, "damage": 10, "DamageToBase": 5, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 10, "push": 15, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_girl_z_jailerrifle": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0.5, "damage": 10, "DamageToBase": 5, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 10, "push": 15, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_girl_z_sniper": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0.5, "damage": 10, "DamageToBase": 5, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 10, "push": 15, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_z_girl_z_carol": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0.5, "damage": 10, "DamageToBase": 5, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 10, "push": 15, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_puke_z_glenn": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 52, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 5, "poisonResist": 0, "presentationOrderId": 4, "push": 0, "pushResist": 0.2, "rageCost": 2, "rangeResist": 0, "spawnShift": -22, "specialActionProbability": 25}, "unit_z_witch_z_sonya": {"aggression": 18, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -48, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 13, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 0, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 30, "poisonResist": 0, "presentationOrderId": 14, "push": 0, "pushResist": 0, "rageCost": 6, "rangeResist": 0, "spawnShift": -20, "specialActionProbability": 0}, "unit_z_soldier_z_h_hero": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 14, "DamageToBase": 20, "explosionResist": 0, "fireResist": 0, "healthPoints": 55, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 12, "push": 0, "pushResist": 0.4, "rageCost": 1, "rangeResist": 0.3, "spawnShift": -10, "specialActionProbability": 0}, "unit_z_soldier_z_lightsodier": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 30, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 14, "DamageToBase": 20, "explosionResist": 0, "fireResist": 0, "healthPoints": 55, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 12, "push": 0, "pushResist": 0.4, "rageCost": 1, "rangeResist": 0.3, "spawnShift": -10, "specialActionProbability": 0}, "unit_z_puke_z_jailer": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 52, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 5, "poisonResist": 0, "presentationOrderId": 4, "push": 0, "pushResist": 0.2, "rageCost": 2, "rangeResist": 0, "spawnShift": -22, "specialActionProbability": 25}, "unit_z_puke_z_austin": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 52, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 5, "poisonResist": 0, "presentationOrderId": 4, "push": 0, "pushResist": 0.2, "rageCost": 2, "rangeResist": 0, "spawnShift": -22, "specialActionProbability": 25}, "unit_z_monk_saw": {"aggression": 50, "AggressionPenalty": 0.8, "agility": 85, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 5, "DamageToBase": 5, "explosionResist": 0, "fireResist": 0, "healthPoints": 42, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 44, "push": "15 15", "pushResist": 0.3, "rageCost": 1, "rangeResist": 0, "spawnShift": -62, "specialActionProbability": 0}, "unit_z_naked_berserker": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 60, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 6, "poisonResist": 0, "presentationOrderId": 1, "push": 0, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": -21, "specialActionProbability": 5}, "unit_z_girl_queen": {"aggression": 25, "AggressionPenalty": 0.8, "agility": 100, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -64, "cooldown": 0, "cost": 0, "critChance": 0.5, "damage": 10, "DamageToBase": 5, "explosionResist": 0, "fireResist": 0, "healthPoints": 39, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 100, "MoralDistanceForward": 200, "moveSpeed": 16, "poisonResist": 0, "presentationOrderId": 10, "push": 15, "pushResist": 0, "rageCost": 2, "rangeResist": 0, "spawnShift": 0, "specialActionProbability": 0}, "unit_z_puke_agent": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 52, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 5, "poisonResist": 0, "presentationOrderId": 4, "push": 0, "pushResist": 0.2, "rageCost": 2, "rangeResist": 0, "spawnShift": -22, "specialActionProbability": 25}, "unit_z_puke_agent002": {"aggression": 13, "AggressionPenalty": 0.8, "agility": 50, "AgilityPenalty": 0.8, "antiFireResist": 0, "antiRangeResist": 0, "AoeHalfZ": 1, "AoeX": 18, "BDMoral": -8, "cooldown": 0, "cost": 0, "critChance": 0, "damage": 10, "DamageToBase": 10, "explosionResist": 0, "fireResist": 0, "healthPoints": 52, "IdleCount": 1, "meleeResist": 0, "Moral": 0, "MoralDistanceBack": 50, "MoralDistanceForward": 100, "moveSpeed": 5, "poisonResist": 0, "presentationOrderId": 4, "push": 0, "pushResist": 0.2, "rageCost": 2, "rangeResist": 0, "spawnShift": -22, "specialActionProbability": 25}, "unitlimits_h_builder": {"Fortune": "39,39,39,39,39,39,39,39,47,47,47,47,47,47,47,47,55,55,55", "Health": "62,62,62,62,62,62,62,62,70,70,70,70,70,70,70,70,78,78,78", "Melee": "32,32,32,32,32,32,32,32,40,40,40,40,40,40,40,40,48,48,48", "Speed": "12,12,12,12,12,12,12,12,14,14,14,14,14,14,14,14,16,16,16"}, "unitlimits_h_carlos": {"Fortune": "39,39,39,39,39,39,39,39,47,47,47,47,47,47,47,47,55,55,55", "Health": "30,30,30,30,30,30,30,30,38,38,38,38,38,38,38,38,46,46,46", "Melee": "3,3,3,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4", "Ranged": "14,14,14,14,14,14,14,14,22,22,22,22,22,22,22,22,30,30,30", "Speed": "9,9,9,9,9,9,9,9,11,11,11,11,11,11,11,11,13,13,13"}, "unitlimits_h_charlotte": {"Fortune": "39,39,39,39,39,39,39,39,47,47,47,47,47,47,47,47,55,55,55", "Health": "35,35,35,35,35,35,35,35,43,43,43,43,43,43,43,43,51,51,51", "Melee": "3,3,3,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4", "Ranged": "60,60,60,60,60,60,60,60,68,68,68,68,68,68,68,68,76,76,76", "Speed": "12,12,12,12,12,12,12,12,14,14,14,14,14,14,14,14,16,16,16"}, "unitlimits_h_cheechmarin": {"Fortune": "39,39,39,39,39,39,39,39,47,47,47,47,47,47,47,47,55,55,55", "Health": "41,41,41,41,41,41,41,41,49,49,49,49,49,49,49,49,57,57,57", "Melee": "35,35,35,35,35,35,35,35,43,43,43,43,43,43,43,43,51,51,51", "Speed": "18,18,18,18,18,18,18,18,20,20,20,20,20,20,20,20,22,22,22"}, "unitlimits_h_chopper": {"Fortune": "41,41,41,41,41,41,41,41,49,49,49,49,49,49,49,49,57,57,57", "Health": "308,308,308,308,308,308,308,308,316,316,316,316,316,316,316,316,324,324,324", "Melee": "26,26,26,26,26,26,26,26,34,34,34,34,34,34,34,34,42,42,42", "Speed": "6,6,6,6,6,6,6,6,8,8,8,8,8,8,8,8,10,10,10"}, "unitlimits_h_cop": {"Fortune": "39,39,39,39,39,39,39,39,47,47,47,47,47,47,47,47,55,55,55", "Health": "32,32,32,32,32,32,32,32,40,40,40,40,40,40,40,40,48,48,48", "Melee": "3,3,3,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4", "Ranged": "22,22,22,22,22,22,22,22,30,30,30,30,30,30,30,30,38,38,38", "Speed": "8,8,8,8,8,8,8,8,10,10,10,10,10,10,10,10,12,12,12"}, "unitlimits_h_farmer": {"Fortune": "39,39,39,39,39,39,39,39,47,47,47,47,47,47,47,47,55,55,55", "Health": "26,26,26,26,26,26,26,26,34,34,34,34,34,34,34,34,42,42,42", "Melee": "3,3,3,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4", "Ranged": "17,17,17,17,17,17,17,17,25,25,25,25,25,25,25,25,33,33,33", "Speed": "8,8,8,8,8,8,8,8,10,10,10,10,10,10,10,10,12,12,12"}, "unitlimits_h_glenn": {"Fortune": "39,39,39,39,39,39,39,39,47,47,47,47,47,47,47,47,55,55,55", "Health": "35,35,35,35,35,35,35,35,43,43,43,43,43,43,43,43,51,51,51", "Melee": "17,17,17,17,17,17,17,17,25,25,25,25,25,25,25,25,33,33,33", "Speed": "18,18,18,18,18,18,18,18,20,20,20,20,20,20,20,20,22,22,22"}, "unitlimits_h_naked": {"Fortune": "39,39,39,39,39,39,39,39,47,47,47,47,47,47,47,47,55,55,55", "Health": "35,35,35,35,35,35,35,35,43,43,43,43,43,43,43,43,51,51,51", "Melee": "17,17,17,17,17,17,17,17,25,25,25,25,25,25,25,25,33,33,33", "Speed": "18,18,18,18,18,18,18,18,20,20,20,20,20,20,20,20,22,22,22"}, "unitlimits_h_puke": {"Fortune": "39,39,39,39,39,39,39,39,47,47,47,47,47,47,47,47,55,55,55", "Health": "44,44,44,44,44,44,44,44,52,52,52,52,52,52,52,52,60,60,60", "Melee": "17,17,17,17,17,17,17,17,25,25,25,25,25,25,25,25,33,33,33", "Speed": "16,16,16,16,16,16,16,16,18,18,18,18,18,18,18,18,20,20,20"}, "unitlimits_h_welder": {"Fortune": "39,39,39,39,39,39,39,39,47,47,47,47,47,47,47,47,55,55,55", "Health": "129,129,129,129,129,129,129,129,137,137,137,137,137,137,137,137,145,145,145", "Melee": "31,31,31,31,31,31,31,31,39,39,39,39,39,39,39,39,47,47,47", "Speed": "22,22,22,22,22,22,22,22,24,24,24,24,24,24,24,24,26,26,26"}, "upgCost_h_agent": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_austin": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_ballisticShielder": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_berserker": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_builder": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_carlos": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_carol": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_charlotte": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_cheechmarin": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_chopper": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_cop": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_farmer": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_firefighter": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_flamethrower": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_glenn": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_grenader": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_guard": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_heavyGuard": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_hero": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_jailer": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_jailerrifle": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_judi": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_lester": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_lightSoldier": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_mechanic": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_medic": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_naked": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_puke": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_queen": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_saw": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_sniper": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_soldierCap": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_sonya": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_specops": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_swat": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_h_welder": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_item_barrel": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_item_bomb": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_item_generator": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_item_medkit": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_item_molotov": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_item_nitrogen": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_item_red_barrel": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "upgCost_item_turret": "19,53,92,112,225,277,352,461,622,860,1212,1733,2506,3654,5361,7900,11681,17312,25702", "weeklyEvent_cornFarm_barricadeLevel_0": {"BanditBillCooldown": 60, "BanditBillDamage": 40, "BanditBillShootingDuration": 3, "BarricadeHP": 1000, "Level": 1, "Reward": "RandomItems 1 CommonItem", "Wave": "5 b_naked b_naked b_naked b_naked b_crazy 7 b_builder b_builder 15 b_farmer mis b_crazy b_crazy 15 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_1": {"BanditBillCooldown": 40, "BanditBillDamage": 40, "BanditBillShootingDuration": 3, "BarricadeHP": 1600, "Level": 2, "Reward": "RandomItems 1 CommonItem", "Wave": "5 b_naked b_naked b_naked b_naked b_builder b_crazy 7 b_builder b_builder 15 b_farmer b_crazy b_crazy b_crazy 15 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_10": {"BanditBillCooldown": 15, "BanditBillDamage": 40, "BanditBillShootingDuration": 4, "BarricadeHP": 7000, "Level": 11, "Reward": "RandomItems 1 RareItem", "Wave": "5 b_naked b_naked b_naked b_builder 2 b_crazy b_crazy 2 b_crazy b_crazy 2 b_biker b_crazy b_farmer b_biker 6 b_biker 8 b_biker b_farmer b_biker b_crazy b_crazy b_crazy 9 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_11": {"BanditBillCooldown": 15, "BanditBillDamage": 40, "BanditBillShootingDuration": 4, "BarricadeHP": 7600, "Level": 12, "Reward": "RandomItems 1 RareItem", "Wave": "5 b_naked b_naked b_naked b_builder b_biker 2 b_crazy b_crazy b_biker 2 b_crazy b_crazy 2 b_biker b_crazy b_farmer b_biker 6 b_biker 8 b_biker b_farmer b_biker b_crazy b_crazy b_crazy 8 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_12": {"BanditBillCooldown": 10, "BanditBillDamage": 40, "BanditBillShootingDuration": 5, "BarricadeHP": 8200, "Level": 13, "Reward": "RandomItems 2 RareItem", "Wave": "5 b_naked b_naked b_naked b_builder b_biker 2 b_crazy b_crazy b_naked b_biker 2 b_crazy b_crazy 2 b_biker b_crazy b_farmer b_biker 6 b_biker 8 b_biker b_farmer b_biker b_crazy b_crazy b_crazy 8 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_13": {"BanditBillCooldown": 10, "BanditBillDamage": 40, "BanditBillShootingDuration": 5, "BarricadeHP": 8800, "Level": 14, "Reward": "RandomItems 2 RareItem", "Wave": "5 b_naked b_naked b_naked b_builder b_biker 2 b_crazy b_crazy b_naked b_biker 2 b_crazy b_crazy 2 b_biker b_crazy b_farmer b_biker 6 b_biker 8 b_biker b_farmer b_biker b_crazy b_crazy b_crazy 8 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_14": {"BanditBillCooldown": 8, "BanditBillDamage": 40, "BanditBillShootingDuration": 5, "BarricadeHP": 9400, "Level": 15, "Reward": "RandomItems 2 RareItem", "Wave": "5 b_naked b_naked b_naked b_builder b_biker 2 b_crazy b_crazy b_naked b_biker 2 b_crazy b_crazy 2 b_biker b_crazy b_farmer b_biker 6 b_biker 8 b_biker b_farmer b_biker b_crazy b_crazy b_crazy 8 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_15": {"BanditBillCooldown": 8, "BanditBillDamage": 50, "BanditBillShootingDuration": 6, "BarricadeHP": 10000, "Level": 16, "Reward": "RandomItems 3 RareItem", "Wave": "5 b_naked b_naked b_naked b_builder b_biker 2 b_crazy b_crazy b_naked b_biker 2 b_crazy b_crazy 2 b_biker b_crazy b_farmer b_biker 6 b_biker 8 b_biker b_farmer b_biker b_crazy b_crazy b_crazy 8 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_16": {"BanditBillCooldown": 5, "BanditBillDamage": 60, "BanditBillShootingDuration": 6, "BarricadeHP": 12000, "Level": 17, "Reward": "RandomItems 3 RareItem", "Wave": "5 b_naked b_naked b_naked b_builder b_biker 2 b_crazy b_crazy b_naked b_biker 2 b_crazy b_crazy 2 b_biker b_crazy b_farmer b_biker 6 b_biker 8 b_biker b_farmer b_biker b_crazy b_crazy b_crazy 8 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_17": {"BanditBillCooldown": 5, "BanditBillDamage": 70, "BanditBillShootingDuration": 6, "BarricadeHP": 15000, "Level": 18, "Reward": "RandomItems 1 LegendaryItem", "Wave": "5 b_naked b_naked b_naked b_builder b_biker 2 b_crazy b_crazy b_naked b_biker 2 b_crazy b_crazy 2 b_biker b_crazy b_farmer b_biker 6 b_biker 8 b_biker b_farmer b_biker b_crazy b_crazy b_crazy 8 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_18": {"BanditBillCooldown": 5, "BanditBillDamage": 80, "BanditBillShootingDuration": 8, "BarricadeHP": 20000, "Level": 19, "Reward": "RandomItems 2 LegendaryItem", "Wave": "5 b_naked b_naked b_naked b_builder b_biker 2 b_crazy b_crazy b_naked b_biker 2 b_crazy b_crazy 2 b_biker b_crazy b_farmer b_biker 6 b_biker 8 b_biker b_farmer b_biker b_crazy b_crazy b_crazy 8 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_2": {"BanditBillCooldown": 40, "BanditBillDamage": 40, "BanditBillShootingDuration": 3, "BarricadeHP": 2200, "Level": 3, "Reward": "RandomItems 2 CommonItem", "Wave": "5 b_naked b_naked b_naked b_builder b_builder b_crazy b_biker 7 b_builder b_builder 15 b_farmer b_crazy b_crazy b_crazy 15 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_3": {"BanditBillCooldown": 40, "BanditBillDamage": 40, "BanditBillShootingDuration": 3, "BarricadeHP": 2800, "Level": 4, "Reward": "RandomItems 2 CommonItem", "Wave": "5 b_naked b_naked b_naked b_builder b_builder b_crazy b_farmer b_biker 7 b_builder b_builder 15 b_farmer b_crazy b_crazy b_crazy 15 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_4": {"BanditBillCooldown": 30, "BanditBillDamage": 40, "BanditBillShootingDuration": 3, "BarricadeHP": 3400, "Level": 5, "Reward": "RandomItems 2 CommonItem", "Wave": "5 b_naked b_naked b_naked b_builder b_builder b_crazy b_farmer b_biker 7 b_builder b_builder b_biker 15 b_farmer b_crazy b_crazy b_crazy 15 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_5": {"BanditBillCooldown": 30, "BanditBillDamage": 40, "BanditBillShootingDuration": 3, "BarricadeHP": 4000, "Level": 6, "Reward": "RandomItems 2 CommonItem", "Wave": "5 b_naked b_naked b_naked b_builder b_builder b_crazy b_farmer b_biker 7 b_builder b_builder b_biker 15 b_farmer b_biker b_crazy b_crazy b_crazy 15 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_6": {"BanditBillCooldown": 25, "BanditBillDamage": 40, "BanditBillShootingDuration": 3, "BarricadeHP": 4600, "Level": 7, "Reward": "RandomItems 2 CommonItem", "Wave": "5 b_naked b_naked b_naked b_builder b_builder b_crazy b_farmer b_biker 6 b_builder b_builder b_biker 15 b_farmer b_biker b_crazy b_crazy b_crazy 15 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_7": {"BanditBillCooldown": 25, "BanditBillDamage": 40, "BanditBillShootingDuration": 3, "BarricadeHP": 5200, "Level": 8, "Reward": "RandomItems 3 CommonItem", "Wave": "5 b_naked b_naked b_naked b_builder b_builder b_crazy b_farmer b_biker 6 b_builder b_builder b_biker 13 b_farmer b_biker b_crazy b_crazy b_crazy 13 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_8": {"BanditBillCooldown": 20, "BanditBillDamage": 40, "BanditBillShootingDuration": 3, "BarricadeHP": 5800, "Level": 9, "Reward": "RandomItems 3 CommonItem", "Wave": "5 b_naked b_naked b_naked b_builder b_builder b_crazy b_farmer b_biker 6 b_builder b_builder b_biker 10 b_farmer b_biker b_crazy b_crazy b_crazy 10 b_biker 1"}, "weeklyEvent_cornFarm_barricadeLevel_9": {"BanditBillCooldown": 20, "BanditBillDamage": 40, "BanditBillShootingDuration": 4, "BarricadeHP": 6400, "Level": 10, "Reward": "RandomItems 1 RareItem", "Wave": "5 b_naked b_naked b_naked b_builder b_builder b_biker b_crazy b_farmer b_biker 6 b_builder b_builder b_biker 10 b_biker b_farmer b_biker b_crazy b_crazy b_crazy 10 b_biker 1"}, "weeklyEvent_cornFarm_personalScoreForLootBox": 150, "weeklyEvent_instance": {"FreeAttempts": 5, "KillsToComplete": 2000000, "StartCost": 350}, "weeklyEvent_rage_finalWave": "z_girl-3 z_witch-5 z_kamikaze-3 3 z_kamikaze-3 3 z_kamikaze-3 3", "weeklyEvent_rage_freeAttemptsPerDay": 2, "weeklyEvent_rage_fuelStartCost": 4, "weeklyEvent_rage_grenadeCooldown": 5, "weeklyEvent_rage_grenadeCost": 10, "weeklyEvent_rage_grenadeDamage": 50, "weeklyEvent_rage_humanWaves": "h_toadstool 1 10", "weeklyEvent_rage_personalScoreForLootBox": 150, "weeklyEvent_rage_startRageValue": 100, "weeklyEvent_rage_startWave": "z_cop z_puke", "weeklyEvent_rage_wave_0": {"IterationsCount": 1, "Wave": "z_naked-2 8"}, "weeklyEvent_rage_wave_1": {"IterationsCount": 2, "Wave": "z_nakedRun-2 10"}, "weeklyEvent_rage_wave_10": {"IterationsCount": 1, "Wave": "z_hunter-2 1 z_kamikaze-3 1"}, "weeklyEvent_rage_wave_11": {"IterationsCount": 1, "Wave": "z_cheechmarin-4 z_yellow-2 z_skeletonMillitary-5 z_kamikaze-3 5"}, "weeklyEvent_rage_wave_12": {"IterationsCount": 1, "Wave": "z_girl-3 1 z_kamikaze-3 1"}, "weeklyEvent_rage_wave_13": {"IterationsCount": 1, "Wave": "z_yellow-4 z_soldierfat-2 z_skeletonMillitary-4 z_kamikaze-3 5"}, "weeklyEvent_rage_wave_14": {"IterationsCount": 1, "Wave": "z_girl-3 1 z_demon-2 z_skeletonMillitary-7 5 z_girl-3 1 z_kamikaze-3 1"}, "weeklyEvent_rage_wave_15": {"IterationsCount": 1, "Wave": "z_bulletproof-3 z_soldierfat-2 z_skeletonMillitary-3 z_kamikaze-3 5"}, "weeklyEvent_rage_wave_16": {"IterationsCount": 3, "Wave": "z_girl-3 z_witch-5 z_kamikaze-3 3 z_kamikaze-3 3 z_kamikaze-3 3 "}, "weeklyEvent_rage_wave_17": {"IterationsCount": 1, "Wave": "z_twins-3 z_prisonerLocusts-3 z_monk-3 z_kamikaze-3 5"}, "weeklyEvent_rage_wave_18": {"IterationsCount": 1, "Wave": "z_alienCop-3 z_swat-2 z_skeletonMillitary-3 z_monk-2 z_kamikaze-3 5"}, "weeklyEvent_rage_wave_2": {"IterationsCount": 2, "Wave": "z_naked-4 z_puke-2 z_nakedRun-3 20"}, "weeklyEvent_rage_wave_3": {"IterationsCount": 1, "Wave": "z_cop-5 5"}, "weeklyEvent_rage_wave_4": {"IterationsCount": 1, "Wave": "z_witch 1 z_puke-4 z_cop-2 z_nakedRun-2 15"}, "weeklyEvent_rage_wave_5": {"IterationsCount": 1, "Wave": "z_megavolt-2 z_hunter-1 z_witch-1 15"}, "weeklyEvent_rage_wave_6": {"IterationsCount": 1, "Wave": "z_witch-1 5 z_kamikaze 15"}, "weeklyEvent_rage_wave_7": {"IterationsCount": 1, "Wave": "z_builder-3 z_cop-2 z_nakedRun-2 z_hunter-1 15"}, "weeklyEvent_rage_wave_8": {"IterationsCount": 1, "Wave": "z_witch-3 10"}, "weeklyEvent_rage_wave_9": {"IterationsCount": 1, "Wave": "z_cop-5 5"}, "weeklyEvent_wallMart_finalWave": "z_witch-4 z_bulletproof-1 z_kamikaze-4 z_girl-4 3", "weeklyEvent_wallMart_onHumanRunAwayCoinsCount": 20, "weeklyEvent_wallMart_onHumanRunAwayCoinsDropChance": 33, "weeklyEvent_wallMart_personalScoreForLootBox": 150, "weeklyEvent_wallMart_startWave": "z_naked-3 z_puke-3 z_builder-1 z_cop-1", "weeklyEvent_wallMart_wave_0": {"IterationsCount": 2, "Wave": "z_naked-2 4"}, "weeklyEvent_wallMart_wave_1": {"IterationsCount": 1, "Wave": "z_nakedRun-4 1"}, "weeklyEvent_wallMart_wave_10": {"IterationsCount": 2, "Wave": "z_cheechmarin-4 z_yellow-2 z_skeletonMillitary-5 5"}, "weeklyEvent_wallMart_wave_11": {"IterationsCount": 1, "Wave": "z_girl-3 1 z_kamikaze 1"}, "weeklyEvent_wallMart_wave_12": {"IterationsCount": 2, "Wave": "z_yellow-4 z_soldierfat-2 z_skeletonMillitary-4 5"}, "weeklyEvent_wallMart_wave_13": {"IterationsCount": 1, "Wave": "z_girl-3 1 z_demon-2 z_skeletonMillitary-7 5 z_girl-3 1 z_kamikaze 1"}, "weeklyEvent_wallMart_wave_14": {"IterationsCount": 2, "Wave": "z_bulletproof-3 z_soldierfat-2 z_skeletonMillitary-3 z_kamikaze-3 5"}, "weeklyEvent_wallMart_wave_15": {"IterationsCount": 2, "Wave": "z_girl-3 z_witch-5 3"}, "weeklyEvent_wallMart_wave_16": {"IterationsCount": 1, "Wave": "z_kamikaze-3 1"}, "weeklyEvent_wallMart_wave_2": {"IterationsCount": 2, "Wave": "z_naked-4 z_puke-2 z_nakedRun-3 5"}, "weeklyEvent_wallMart_wave_3": {"IterationsCount": 1, "Wave": "z_witch-3 1 z_puke-4 z_cop-2 z_nakedRun-3 5 z_witch-3 1"}, "weeklyEvent_wallMart_wave_4": {"IterationsCount": 1, "Wave": "z_megavolt-2 z_hunter-3 z_witch-3 5"}, "weeklyEvent_wallMart_wave_5": {"IterationsCount": 4, "Wave": "z_witch-3 5 z_kamikaze 1"}, "weeklyEvent_wallMart_wave_6": {"IterationsCount": 2, "Wave": "z_builder-3 z_cop-2 z_nakedRun-3 z_hunter-3 5"}, "weeklyEvent_wallMart_wave_7": {"IterationsCount": 1, "Wave": "z_witch-2 2"}, "weeklyEvent_wallMart_wave_8": {"IterationsCount": 2, "Wave": "z_cheechmarin-2 5"}, "weeklyEvent_wallMart_wave_9": {"IterationsCount": 1, "Wave": "z_hunter-2 1 z_kamikaze 1"}, "weeklyEvent_xmas_boxDropChance": 75, "weeklyEvent_xmas_finalWave": "z_witch-4 z_bulletproof-1 z_kamikaze-4 z_xmasGirl-4 3", "weeklyEvent_xmas_maxBoxesPerBattle": 200, "weeklyEvent_xmas_personalReward_0": {"Reward": "coins 350", "Score": 25}, "weeklyEvent_xmas_personalReward_1": {"Reward": "paint christmas", "Score": 75}, "weeklyEvent_xmas_personalReward_2": {"Reward": "shop_box1 5", "Score": 150}, "weeklyEvent_xmas_personalReward_3": {"Reward": "coins 550", "Score": 250}, "weeklyEvent_xmas_personalReward_4": {"Reward": "shop_box1 8", "Score": 400}, "weeklyEvent_xmas_personalReward_5": {"Reward": "unit h_lester", "Score": 500}, "weeklyEvent_xmas_startWave": "z_xmasNaked-3 z_puke-3 z_builder-1", "weeklyEvent_xmas_wave_0": {"IterationsCount": 2, "Wave": "z_xmasNaked-2 4"}, "weeklyEvent_xmas_wave_1": {"IterationsCount": 1, "Wave": "z_xmasNakedRun-4 1"}, "weeklyEvent_xmas_wave_10": {"IterationsCount": 2, "Wave": "z_cheechmarin-4 z_yellow-2 z_skeletonMillitary-5 5"}, "weeklyEvent_xmas_wave_11": {"IterationsCount": 1, "Wave": "z_xmasGirl-3 1 z_kamikaze 1"}, "weeklyEvent_xmas_wave_12": {"IterationsCount": 2, "Wave": "z_yellow-4 z_soldierfat-2 z_skeletonMillitary-4 5"}, "weeklyEvent_xmas_wave_13": {"IterationsCount": 1, "Wave": "z_xmasGirl-3 1 z_demon-2 z_skeletonMillitary-7 5 z_xmasGirl-3 1 z_kamikaze 1"}, "weeklyEvent_xmas_wave_14": {"IterationsCount": 2, "Wave": "z_bulletproof-3 z_soldierfat-2 z_skeletonMillitary-3 z_kamikaze-3 5"}, "weeklyEvent_xmas_wave_15": {"IterationsCount": 10, "Wave": "z_xmasGirl-3 z_witch-5 3"}, "weeklyEvent_xmas_wave_16": {"IterationsCount": 1, "Wave": "z_kamikaze-3 1"}, "weeklyEvent_xmas_wave_2": {"IterationsCount": 2, "Wave": "z_xmasNaked-4 z_puke-2 z_xmasNakedRun-3 5"}, "weeklyEvent_xmas_wave_3": {"IterationsCount": 1, "Wave": "z_witch-3 1 z_puke-4 z_xmasCop-2 z_xmasNakedRun-3 5 z_witch-3 1"}, "weeklyEvent_xmas_wave_4": {"IterationsCount": 2, "Wave": "z_megavolt-2 z_hunter-3 z_witch-3 5"}, "weeklyEvent_xmas_wave_5": {"IterationsCount": 1, "Wave": "z_witch-3 5 z_kamikaze 1"}, "weeklyEvent_xmas_wave_6": {"IterationsCount": 2, "Wave": "z_builder-3 z_xmasCop-2 z_xmasNakedRun-3 z_hunter-3 5"}, "weeklyEvent_xmas_wave_7": {"IterationsCount": 1, "Wave": "z_witch-2 2"}, "weeklyEvent_xmas_wave_8": {"IterationsCount": 2, "Wave": "z_cheechmarin-2 5"}, "weeklyEvent_xmas_wave_9": {"IterationsCount": 1, "Wave": "z_hunter-2 1 z_kamikaze 1"}, "weeklyEventReward_1": {"Range": "1-100", "Reward": "shop_box1 10"}, "weeklyEventReward_2": {"Range": "101-200", "Reward": "coins 1500"}, "weeklyEventReward_3": {"Range": "0-0", "Reward": "coins 500"}, "CorpseType": {"h_farmer": "z_farmer", "h_mechanic": "z_mechanic", "h_naked": "z_farmer"}}