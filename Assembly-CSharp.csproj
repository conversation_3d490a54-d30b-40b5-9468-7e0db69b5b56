﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{CCEA74AA-9565-EDE7-56D6-14609D5A4B58}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2018_4_33;UNITY_2018_4;UNITY_2018;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;UNITY_ANALYTICS;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_DUCK_TYPING;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_SPRITES;ENABLE_GRID;ENABLE_TILEMAP;ENABLE_TERRAIN;ENABLE_TEXTURE_STREAMING;ENABLE_DIRECTOR;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_WEBCAM;ENABLE_WWW;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_HUB;ENABLE_CLOUD_PROJECT_ID;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_TIMELINE;ENABLE_EDITOR_METRICS;ENABLE_EDITOR_METRICS_CACHING;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;INCLUDE_DYNAMIC_GI;INCLUDE_GI;ENABLE_MONO_BDWGC;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;INCLUDE_PUBNUB;ENABLE_VIDEO;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_LOCALIZATION;PLATFORM_STANDALONE_WIN;PLATFORM_STANDALONE;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_SUBSTANCE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_UNITYWEBREQUEST;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_EVENT_QUEUE;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;ENABLE_VR;ENABLE_AR;ENABLE_WEBSOCKET_HOST;ENABLE_MONO;NET_4_6;ENABLE_PROFILER;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;UNITY_PRO_LICENSE;_SELF_DEBUG;_NO_ASB;_NO_NET;Change_Res_3D1;USE_CAR_3D;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEngine/UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\2018.4.33f1\Editor\Data\Managed/UnityEditor.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
     <Compile Include="Assets\AtmosphereFX\scripts\AtmoCameraController.cs" />
     <Compile Include="Assets\AtmosphereFX\scripts\Cleanup.cs" />
     <Compile Include="Assets\AtmosphereFX\scripts\Lightning.cs" />
     <Compile Include="Assets\AtmosphereFX\scripts\PlayEffect.cs" />
     <Compile Include="Assets\AtmosphereFX\scripts\Visual.cs" />
     <Compile Include="Assets\ConsolePro\ConsoleProDebug.cs" />
     <Compile Include="Assets\ConsolePro\Remote\ConsoleProRemoteServer.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\BaseChannel.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\ConnectionRequest.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\INetEventListener.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\Layers\Crc32cLayer.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\Layers\PacketLayerBase.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\Layers\XorEncryptLayer.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\NatPunchModule.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\NetConstants.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\NetDebug.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\NetManager.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\NetPacket.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\NetPacketPool.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\NetPeer.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\NetSocket.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\NetStatistics.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\NetUtils.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\ReliableChannel.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\SequencedChannel.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\Utils\CRC32C.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\Utils\FastBitConverter.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\Utils\INetSerializable.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\Utils\NetDataReader.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\Utils\NetDataWriter.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\Utils\NetPacketProcessor.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\Utils\NetSerializer.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\Utils\NtpPacket.cs" />
     <Compile Include="Assets\ConsolePro\Remote\LiteNetLib\Utils\NtpRequest.cs" />
     <Compile Include="Assets\EnergyQuestPanel.cs" />
     <Compile Include="Assets\Epic Toon FX\Demo\Scripts\ETFXButtonScript.cs" />
     <Compile Include="Assets\Epic Toon FX\Demo\Scripts\ETFXEffectController.cs" />
     <Compile Include="Assets\Epic Toon FX\Demo\Scripts\ETFXEffectControllerPooled.cs" />
     <Compile Include="Assets\Epic Toon FX\Demo\Scripts\ETFXEffectCycler.cs" />
     <Compile Include="Assets\Epic Toon FX\Demo\Scripts\ETFXFireProjectile.cs" />
     <Compile Include="Assets\Epic Toon FX\Demo\Scripts\ETFXLoopScript.cs" />
     <Compile Include="Assets\Epic Toon FX\Demo\Scripts\ETFXMouseOrbit.cs" />
     <Compile Include="Assets\Epic Toon FX\Demo\Scripts\ETFXProjectileScript.cs" />
     <Compile Include="Assets\Epic Toon FX\Demo\Scripts\ETFXSceneManager.cs" />
     <Compile Include="Assets\Epic Toon FX\Demo\Scripts\ETFXSpriteBouncer.cs" />
     <Compile Include="Assets\Epic Toon FX\Demo\Scripts\ETFXTarget.cs" />
     <Compile Include="Assets\Epic Toon FX\Demo\Scripts\VFX Library\ParticleEffectsLibrary.cs" />
     <Compile Include="Assets\Epic Toon FX\Demo\Scripts\VFX Library\PEButtonScript.cs" />
     <Compile Include="Assets\Epic Toon FX\Demo\Scripts\VFX Library\UICanvasManager.cs" />
     <Compile Include="Assets\Epic Toon FX\Scripts\ETFXLightFade.cs" />
     <Compile Include="Assets\Epic Toon FX\Scripts\ETFXPitchRandomizer.cs" />
     <Compile Include="Assets\Epic Toon FX\Scripts\ETFXRotation.cs" />
     <Compile Include="Assets\Hovl Studio\Sword slash VFX\Demo scene\CameraHolder.cs" />
     <Compile Include="Assets\Hovl Studio\Toon Projectiles 2\Demo scene\DemoShooting.cs" />
     <Compile Include="Assets\Hovl Studio\Toon Projectiles 2\Scripts\AutoDestroyPS.cs" />
     <Compile Include="Assets\Hovl Studio\Toon Projectiles 2\Scripts\ProjectileMover.cs" />
     <Compile Include="Assets\JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_AutoStopLoopedEffect.cs" />
     <Compile Include="Assets\JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_Demo.cs" />
     <Compile Include="Assets\JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_Demo_New.cs" />
     <Compile Include="Assets\JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_Demo_RandomDir.cs" />
     <Compile Include="Assets\JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_Demo_RandomDirectionTranslate.cs" />
     <Compile Include="Assets\JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_Demo_RotateCamera.cs" />
     <Compile Include="Assets\JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_Demo_Translate.cs" />
     <Compile Include="Assets\JMO Assets\Cartoon FX (legacy)\Scripts\CFX_AutoDestructShuriken.cs" />
     <Compile Include="Assets\JMO Assets\Cartoon FX (legacy)\Scripts\CFX_AutodestructWhenNoChildren.cs" />
     <Compile Include="Assets\JMO Assets\Cartoon FX (legacy)\Scripts\CFX_AutoRotate.cs" />
     <Compile Include="Assets\JMO Assets\Cartoon FX (legacy)\Scripts\CFX_InspectorHelp.cs" />
     <Compile Include="Assets\JMO Assets\Cartoon FX (legacy)\Scripts\CFX_LightIntensityFade.cs" />
     <Compile Include="Assets\JMO Assets\Cartoon FX (legacy)\Scripts\CFX_ShurikenThreadFix.cs" />
     <Compile Include="Assets\JMO Assets\Cartoon FX (legacy)\Spawn System\CFX_SpawnSystem.cs" />
     <Compile Include="Assets\Lana Studio\Casual RPG VFX\Demo\Scripts\_InputKeyBoard.cs" />
     <Compile Include="Assets\Lana Studio\Casual RPG VFX\Demo\Scripts\ObjectsSwitcher.cs" />
     <Compile Include="Assets\Lana Studio\Casual RPG VFX\Scripts\UVscroll.cs" />
     <Compile Include="Assets\Lux Lit Particles\Demos\Scripts\LuxParticles_ExtendedFlycam.cs" />
     <Compile Include="Assets\Lux Lit Particles\Scripts\LuxParticles_AmbientLighting.cs" />
     <Compile Include="Assets\Lux Lit Particles\Scripts\LuxParticles_DirectionalLight.cs" />
     <Compile Include="Assets\Lux Lit Particles\Scripts\LuxParticles_LocalAmbientLighting.cs" />
     <Compile Include="Assets\MyTools.cs" />
     <Compile Include="Assets\OrdossFX\PolygonalExplosions\PolygonalExplosions_Light.cs" />
     <Compile Include="Assets\OUTPUT\Plugins\VolumetricCrystalMaterials\ExampleScene\MaterialShowcaseRotationScript.cs" />
     <Compile Include="Assets\QFX\MaterializeFX\Demo\SimpleFpsController.cs" />
     <Compile Include="Assets\QFX\MaterializeFX\Scripts\Activator\MfxActivator.cs" />
     <Compile Include="Assets\QFX\MaterializeFX\Scripts\Activator\RayCastShoot.cs" />
     <Compile Include="Assets\QFX\MaterializeFX\Scripts\MaskOffsetDirection.cs" />
     <Compile Include="Assets\QFX\MaterializeFX\Scripts\MfxController.cs" />
     <Compile Include="Assets\QFX\MaterializeFX\Scripts\MfxControllerEditor.cs" />
     <Compile Include="Assets\QFX\MaterializeFX\Scripts\MfxExtensions.cs" />
     <Compile Include="Assets\QFX\MaterializeFX\Scripts\MfxMaterialUtil.cs" />
     <Compile Include="Assets\QFX\MaterializeFX\Scripts\MfxObjectMaterialUpdater.cs" />
     <Compile Include="Assets\QFX\MaterializeFX\Scripts\MfxShaderType.cs" />
     <Compile Include="Assets\QFX\MaterializeFX\Scripts\Shaders\MfxCommonShaderGUI.cs" />
     <Compile Include="Assets\QFX\MaterializeFX\Scripts\Shaders\MfxShaderGui.cs" />
     <Compile Include="Assets\QFX\MaterializeFX\Scripts\Shaders\MfxStandardGui.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AbstractLoginButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Achievement.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AchievementService.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ActionHireButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AdBreakManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AdBreakMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AdBreakSceneController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AdManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AgentBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Alerts.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AlienCopBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Amazon\CognitoIdentity\DeveloperAuthenticatedCredentials.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Amazon\CognitoIdentity\GameCenterPlayerVerificationData.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Amazon\CognitoIdentity\MyCognitoAWSCredentials.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Amazon\CognitoIdentity\MyCognitoIdentityAsyncExecutor.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Amazon\CognitoSync\SyncManager\Internal\MyCognitoSyncStorage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Amazon\CognitoSync\SyncManager\Internal\SQLiteLocalStorageReader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Amazon\CognitoSync\SyncManager\MyDataset.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Amazon\CognitoSync\SyncManager\MySyncFailureEventArgs.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Amazon\CognitoSync\SyncManager\MySyncSuccessEventArgs.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Amazon\Runtime\MyRefreshingAWSCredentials.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AmmoCage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Analytics.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AndroidEmptyCamera.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AndroidIAPValidator.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AndroidNotificationManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AnimatedBattleFx.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AnimatedTiledImage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AnimationEffectHandler.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AnimationEventHandler.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AnimatorsGroup.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Aoe.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeEffect_damageOnce.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeEffect_freeze.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeEffect_igniter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeEffect_spawner.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeEffect_zoneFireDamage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeEffectEvents.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeEffectPrefab.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeObject.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeOptions.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeStorage_Effects.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeStorage_Templates.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeTemplate.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeTemplateBarrelExplZeroLvl.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeTemplateFirefighterExplosion.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeTemplateFreeze.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeTemplateGrenaderGrenadeExplosion.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeTemplateMolotovExplosion.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeTemplateNecromancerZone.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeTemplateRageEventGrenadeExplosion.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeTemplateRedBarrelExplosion.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeTemplateSimpleDust.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AoeTemplateSmallFireExplosion.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ApplyFixedSpriteOffsetY.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ApplyParentByCategory.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ApplySpriteOffsetY.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AppsFlyerManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AppsFlyerTrackerCallbacks.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\APshopContent_valuePack.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\APshopContentController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Arrow.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AskFuelMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AskFuelMenuPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AssetBundleLoader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AssetBundleLoaderConstants.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AssetBundleLoaderUnitsData.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AssetBundleUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AssistantPackOpenMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AssistantPackShopContent.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AssistantPackShopMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AttackHeatMap.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AttackManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AttackOption.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AttackOptions.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Audio\AudioPlayerFake.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Audio\AudioPlayerWWW.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AudioPlayer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AuthenticationChecker.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AutoHideInSecond.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AutomaticUpgradeSection.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AWSCognito.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\AWSKinesisFirehose.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BackgroundBundlesDependencies.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BackgroundManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BalloonAppear.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BalloonMessage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BalloonMessageManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BanditBillBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BanditsBaseScriptable.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BanditSpawner.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BanditTruckBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BanditTruckBumper.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BanditTruckController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BanditTruckScriptableObject.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BanditTruckSpawner.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BarButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BarrelBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BarrelDrop.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BarricadeBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BarricadeBreakParticle.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BarricadePositions.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BaseBattleFx.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BaseBattleUI.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BaseQuestButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BaseTimeBoxesMenuPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BaseTutorialStep.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BattleBus.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BattleDebugEffects.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BattleDeckMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BattleDeckMenuUnit.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BattleEffects.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BattleFadeController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BattleItemUpgradeManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BattleLoot.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BattleParams.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BattleUnit.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BattleUnit3DSkinMeshRenderer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BattleUnitButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BattleUnitColorController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BattleUnitEffects.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BilinearTexturesFix.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BillBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Billing\InApp.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Billing\InAppCoinPacks.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Billing\InAppLegendaryBoxes.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Billing\InAppMyLocalInApp.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Billing\InAppPriceFormatter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Billing\InAppShopBundle.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Billing\InAppSupplyPacks.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Billing\InAppValuePacks.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Billing\ProductId.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Billing\ShopUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Billing\StoreFake.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Billing\StoreService.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Blinker.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BMFont.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BoomerBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BossBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BottomButtonsPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BoundsDummy.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BoxColliderExplosion.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Buff.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BuffActionsManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BuffButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BuffInfo.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BuffManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BuffPanel_buttonsViewer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BuffPanel_v2.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BuilderRunBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BuildNumber.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Bullet.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BusBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BusCharacteristics.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BusCharacteristicsStorage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BusDebugController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BusManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BusPresenter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BusRoof_Item.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BusRoof_ItemsHolder.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BusSkin.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BusSlotButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BusSlotButtonShieldPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BusUpgradeMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\BusUpgradeScriptable.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ButtonBuyBig.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ButtonBuyRealCurrency.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ButtonScale.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ButtonSelector.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CameraController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CameraScroller.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CanvasDebug.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CanvasHudBattle.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CanvasHUDScaler.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarBumper.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarDamageEffects.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CardMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CargoBlow.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarGroupExplosion.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarId.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarLayer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarLayersEffects.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarolBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarPartAttachPoint.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarPartBase.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarPartCategory.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarPartId.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarPartPosition.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarPartPositionsStorage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarPartSimple.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarPartsManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarPartSmoke.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarPartsPacker.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarPartWheels.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarsDoorSpawner.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CarUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CasePanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Cell.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CellCollection.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CellCostIcon.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CellViewer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CephalopodaBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Character.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CharacterAppear.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Charlotte.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ChestPlace.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ChopperBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ClaimButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ClipData.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CoinsVideoQuest.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CollectableItem.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CollectableItemSlot.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CollectBigTextToUnlock.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CollectStarsToUnlock.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ColliderSupportTypeDestroyer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ColorContainer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Colors.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CommonBaseBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CommonBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CommonBundleScriptableMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CommonCar.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CommonContent.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CommonHireButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CommonHumanBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CommonMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CommonReader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CommonUIPrefabs.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CommonUnitBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ConfigAndDateLoader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ConflictResolver.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ConflictSaveMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CooldownManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CopBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Corners.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CornFarmBattleMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CorpseManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CorpseTypeExample.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CostMana.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CostNameplate.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CraftingController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CrankBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CrashHandler.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CrashQuest.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CreatureInstance.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CreaturesOffsets.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CreatureSpeed.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CreatureTemplate.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CrossPromoMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CrossPromoQuest.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Crow.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Crypt\CryptMode.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Crypt\CryptStream.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Crypt\CryptUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CurrenciesMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\CurrencyBar.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DA2\Fx\AnimationEventBloodToBg.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DailyQuestManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Damage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DamageExplosion.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DamageResist.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DamageType.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\EventSystems\MyStandaloneInputModule.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\UI\FontDataV2.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\UnityEngine\UI\ListPool.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\UnityEngine\UI\ObjectPool.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\Battle\CornFarmBarricadeController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\Battle\CornFarmResourcesScriptable.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\Battle\CornFarmScoreController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\Battle\EventCounterBar.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\Battle\IEventCounter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\Battle\RageEventController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\Battle\RageScoreController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\Battle\WallMartCoinsDropController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\Battle\WallMartScoreController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\Battle\XmasBoxesDropController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\Battle\XmasScoreController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\ConfigReaders\CornFarmReader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\ConfigReaders\GeneralReader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\ConfigReaders\RageReader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\ConfigReaders\RangedPersonalReward.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\ConfigReaders\WallMartReader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\ConfigReaders\WaveData.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAQ\WeeklyEvent\ConfigReaders\XmasReader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DataReader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DAZW\HorizontalWrapModeV2.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DebugDefines.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DebugDiscountsBundle.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DebugDiscountsTierPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DebugManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DebugMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DebugMenuCurrency.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DebugMenuDiscounts.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DebugMenuLocalTime.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DebugMenuRewards.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DebugMenuRewardsPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DebugMenuUnitsPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DebugMenuUpgradeUnits.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DebugUnitActions.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DebugUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DebugZombieHireBtn.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DeckItem.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DeeplinksManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DefeatMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DemonBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Destroyer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DestroyListener.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DiagonalLines.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DictionaryUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Disabler.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DisableTouchInterception.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Discount.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DiscountElementShopBundle.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DiscountElementValuePack.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DiscountSaving.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DiscountsConfigLoader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DiscountSequenceCostTiers.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DiscountSequenceItem.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DiscountSequenceRandom.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DiscountShopBundleService.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DiscountsManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DiscountValuePackService.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DisplacementShader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DOAnchorPos.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DodgeableBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DOFadeCanvasGroup.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DogBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DoNotDestroy.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DpiManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DragEvents.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DriverMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DriverMoveFromBus.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DriverState.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DropAreaViewer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\DropManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\EditDeckColleague.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\EditDeckView.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\EggBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ElemsSwitcher.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\EmptyStar.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\EndPVPBattleMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\EnterTheBattleMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\EnterTheTileMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\EnumUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\EventBillBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Events.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\EventSystemUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ExclusiveUnitSection.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ExperienceBar.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Explosions.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ExtraCarsManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FacebookManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FadingBattleFx.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FbComponent.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FbEvents.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FbFriend.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FbFriends.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FbLogin.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FbLoginButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FbPicturesStorage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FbRequests.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FbUserPicture.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FearAndRageAnimation.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FileUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FinalCutScene.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FinalStrikeUI.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FinalWaveUI.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FireManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FireZoneEffect.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FireZonesTracker.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FlamethrowerBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FlyingMoney.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FlyingMoneyExplosion.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FlyingRewardAnimator.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FontChar.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FontCommon.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FontFile.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FontInfo.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FontKerning.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FontLoader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FontPage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FontsController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FontsLibrary.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FPS.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FpsManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FreezeInfo.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FreezeManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FuelBar.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FuelBarSubtract.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxBalloon.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxBarricadeLevel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxBlackSmoke.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxBoomerPart.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxBuff.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxBulletHit.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxBurning.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxBusDamage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxCephalopodaDeathExplosions.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxCephalopodaEggs.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxCreatureCorpse.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxCritEffect.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxDamageText.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxDamageWordEffect.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxDeathBlood.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxDecal.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxDustExplosion.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxEarnedItem.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxEarnedResourceText.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxEmitRateController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxExplosion.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxHealthBar.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxIdleFire.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxLeaf.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxLightingSpawner.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxLoot.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxLootItem.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxLootPackItem.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxMoneyFormatter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxPsyDisplacementExplosion.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxPVPBadge.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxRain.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxReflections.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxRotatingLight.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxSnow.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxXmasLight.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\FxZombieMegaDamage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Game.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\GameController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\GeneratorBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\GooglePlayGamesLoginButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\GraphicExtensions.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Grayscale.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\GreenButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\GreenGlow.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\GrenadeBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\GrenaderBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\GridLayoutGroupExtensions.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\GridLayoutGroupReverse.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\GroundsSpriteHolder.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HealExplosion.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HealthPoints.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HeavyGuardBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\APICallInfo.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\Campaigns\HelpshiftCampaigns.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftAndroidCampaignsDelegate.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftAndroidInboxDelegate.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftAndroidInboxMessage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftAndroidInboxPushNotificationDelegate.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftAndroidLog.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftAuthFailureReason.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftCampaignsAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftDexLoader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftInbox.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftInboxAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftInboxMessage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftInboxMessageActionType.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftInternalLogger.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftJSONUtility.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftLog.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftSdk.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftUser.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\HelpshiftWorker.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\IDexLoaderListener.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\IHelpshiftCampaignsDelegate.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\IHelpshiftInboxDelegate.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\IHelpshiftInboxPushNotificationDelegate.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Helpshift\IWorkerMethodDispatcher.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HelpshiftConfig.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HelpShiftHandler.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HintPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HockeyAppAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HockeyAppIOS.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HockeyAppLogsUploader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HorizontalLines.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HSMiniJSON\Json.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Http\StatusCode.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HttpUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HueShader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HumanBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HumanChopperBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HumanCopBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HumanRangeBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\HumanSpawner.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\IAdAdapter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\IAoeEffectInitializer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\IAoeEffectSubscriber.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\IBounds.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ICommonBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ICommonMenuGetter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\IEditDeckMediator.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\IFireZonesTracker.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\IHardcodedTemplateLoader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\IIOSIAPValidator.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\IKTPlayManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ILoaderExtensions.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ILoadScreen.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ILoadScreenPresenter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ImageWithoutTexture.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\InAppFactory.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\InboxMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\InboxMenuPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\InboxMenuPanelFuel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\InboxQuest.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\InGameCurrencies.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\InGameCurrenciesList.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\InsectBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Interpolate.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\InventoryItem.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\InverseScrollRect.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\InverseScrollRectWatchDog.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\IOSIAPValidator.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\IOSIAPValidatorFake.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\IPoisonExplosionApplyer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\IQuestRewardHandler.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ISize.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ISwipeHandler.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ItemStorage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ItemType.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\IViewMediator.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\JumpingAnimation.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KamikazeBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTAccountManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTAccountManagerAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTAccountManagerCallbackParams.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTAccountManagerCommon.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTAnalytics.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTAnalyticsAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTAnalyticsCommon.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTError.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTLeaderboard.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTLeaderboardAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTLeaderboardCallbackParams.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTLeaderboardCommon.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTPlay.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTPlayAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTPlayCallbackParams.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTPlayCommon.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTPlayDeeplink.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTPlayFakeManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTPlayManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTPlayReward.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTPlayRewardsItem.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTPlaySDKJson\KTJSON.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTPlayStarter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTSettings.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\KTUser.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\LanguageSelectMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\LanguageSelectPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Layers.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Leaderboards\LeaderboardService.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Leaderboards\StubLeaderboard.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\LeafManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\LightsStorage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\LineEquation.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\LinkHint.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\LinksMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\LinksMenuContent.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\LinksMenuPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\LoadingMapEffect.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\LoadScreen.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\LoadScreenPresenter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\LocalizationManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\LogCollector.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\LootPackEvents.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MainThreadQueue.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Map.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MapController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MapEditorCameraScaler.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MapEditorController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MapGenerator.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MapHeroSoundHolder.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MapLoader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MapPrefabHolder.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MapViewer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MaterialGroupReplacer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Materials.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MatrixUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MechanicBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MedicBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MedkitBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MegavoltBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MenuManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Migration.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MissionComplexityText.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MissionInfo.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MissionObjectBarrel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MissionObjectRadio.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MissionObjectsManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MissionObjectUnitPreview.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MissionPrefab.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MissionsStorage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MolotovSpawner.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MoneyManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MonoBehaviourExtensions.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MoraleV2Manager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MovableButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MRGSManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\MusicPlayer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NativeAlert.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NativeAlertButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NativeAlertCallback.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NativeCrash.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NecromancerBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NetworkTimeExample.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NewUnitBattleButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NewUnitFotoMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NewUnitPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NewUpdateMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NitrogenBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NoiseManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NoiseUI.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Notification.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NotificationClaimTimeBox.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NotificationEventFinished.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NotificationEventStarted.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NotificationManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NotificationMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NotificationNoOneEventTry.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NotificationPVPLeagueEnded.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NotificationPVPLeagueEndedSoon.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NotificationRoulette.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NotificationShopBundleEnd.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NotificationStaminaFull.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NotificationType.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NotificationValuePackEnd.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NTP.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NTPOperation.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\NumberUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ObjectPool.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\OfferIndicator.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\OfferMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\OfferMenuElement.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\OfferTimer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\OffsetsStack.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\OnClickSound.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\OnDeadConversion.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\OpenCasesSection.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\OutlineV2.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\OwnedPart.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PackRewardsGenerator.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PaintManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Pair.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PauseController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PauseMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PermanentlyBlockedCellGroup.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PlatformConstants.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PlayerRank.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Point.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PointerEvents.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PoisonManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PositionChanger.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PositionChecker.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PossibleRewards.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PreAlienBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PreEggBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PrefabInstance.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PrefabsHolder.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PressableButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PrisonerBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PrivacyAcceptMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PrivacyManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PrivacyWithdrawMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ProfileMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ProgressBar.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PsyBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PukeBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PushInfo.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PushManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PutItemMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PutItemMenuOptions.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PutItemPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PutItemPanelNoItems.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PutItemPanelOne.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PutItemPanelPool.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PutridBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPAvailableMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPAvailableScriptable.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPBattleGui.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPBattleJudge.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPBattleSettings.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPEnemy.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPHeadPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPHireButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPHireButtonsPool.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPInBattleItemsController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPInstaPromotedPlayers.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPLeagueEndedMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPMenuPlaceHolder.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPMePanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPPanelFake.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPPromotedUserPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPRewards.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPScheduler.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPServer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPServerPasswordHandler.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPUnit.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPUnitCell.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPUnitFakeCell.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPUnitsPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPUnitsPanelsController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPUnitsPanelsPool.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPUnitsSerializer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPUserPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\PVPUserPanelsPool.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\QuestAnimation.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\QuestButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\QuestReward.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\QuestRewardCalcPrice.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\QuestRewardCard.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\QuestRewardCardRefresher.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\QuestRewardSaveToString.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\QuestRewardSplit.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\QuestRewardTokens.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\QuestsPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\QuestUnitMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\QuitConfirmMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RageBar.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RageBattleMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RageEventGrenadeBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RageEventHumanBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RainEffect.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RandomLocalPosition.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RankGainedMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RankStorage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RateGame.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RateGameMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Rectangle.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RectTransformChanger.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RedBarrelBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RedNotification.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RefreshHelper.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RepaintableAnimation.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RepaintableSprite.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RepeatButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ReservedItems.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ResetImageNative.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RestartMissionStaminaVideoQuest.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RetryButtonsController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RetryStaminaButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RetryVideoButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RewardIcon.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RewardPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RewardResources.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RewardType.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RewardView.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Room.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RoomView.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RotateTiledImageEffect.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Roulette.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RouletteController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RouletteLamp.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RouletteLever.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RouletteLightingClock.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RouletteLightingController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RouletteLightingRotation.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RouletteLightingSnake.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RouletteLightingWings.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RouletteManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RouletteMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RouletteRewardChestPlace.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RouletteScriptable.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\RouletteTweens.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SandEffect.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SandParticles.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SaveData.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SaveGame\ILoader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SaveGame\ISaveable.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SaveGame\ISaver.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SaveGame\SaveGameDefaultLoader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SaveGame\SaveGameDefaultSaver.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SaveGame\SaveGameDefaultsCollector.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SaveGame\SaveGameJsonLoader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SaveGame\SaveGameJsonSaver.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SaveGame\SaveGameLoaders.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SaveGame\SaveGameSavers.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SaveGame\SaveGameService.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SaveView.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SawBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SawSoundController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SceneController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SceneSwitcher.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ScreenColorOverlay.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ScreenGlow.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ScreenUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ScriptableBundleMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ScrollBattle.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ScrollMap.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ScrollRectWatchDog.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ScrollWatchDogAbstract.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SectionSelector.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SellerLimits.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SellerManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SellMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SetBattleResourcesScriptable.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SetChangeButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SetCompletedMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SetManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SetSelectCard.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SetSelectMenu_v2.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Settings.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Shaders.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ShadowBehindTile.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ShakeAnimation.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ShielderBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ShielderSmoke.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Shop.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ShopBundleTimer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ShopCardRefresher.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ShopConfig.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ShopMenuSection.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SimpleMenuStorage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SimpleMissionObject.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SingleQuestReward.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SittingCrow.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SittingCrowsPlace.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SkeletonSpawner.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SkipTimeBoxCoinsVideoQuest.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SmallCharacteristicPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SmallCharacteristicsPanelsList.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SmokerBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SoldierCapBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SoundList.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SoundManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SoundsBattle.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SoundsBusUpgrade.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SoundsCommon.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SoundsMap.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SoundsRain.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SoundsRoulette.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SoundsSandstorm.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SoundStorageDestroy.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SoundsZombiepedia.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SpawnEffect.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Spawner.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SpeakingBanditCap.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SpecialMapObject.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SpecialQuest.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SpecialQuestManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SpecopsBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SpecOpsDrop.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SphereBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SplashSceneController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SpriteHolder.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SpriteSet.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\StaminaManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\StaminaMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\StaminaVideoQuest.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\StarBar.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\StarterSceneController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\StateDefinition.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\StatusData.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\StreamingAssetsUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Stripes.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SwitchMenuArrows.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SwitchMenuScroll.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SyncIndicator.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SyncManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\SyncRequestInfo.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TeamMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TeamMenuPanelCollection.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TeamMenuPanelDefenders.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TeamMenuPanelNeedRank.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TeamMenuPanelNotFound.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TeamMenuPanelUnitToUse.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TextV2.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ThreadOperationWithRetry.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ThrowUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TileRewardMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TileShaderData.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TilesStorage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TileUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TimeBox.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TimeBoxes.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TimeBoxesMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TimeBoxesMenuPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TimeBoxesQuestButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TimeBoxInfo.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TimelessWhileChecker.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TimeManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TimeManagerDebug.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TimerMapObject.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TimeUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TipsManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TolerantEnumConverter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TraderContent.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TraderCraftModeLerper.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TraderCraftPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TraderMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TransformExtensions.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TurretBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TutorialBarrel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TutorialEliteUnit.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TutorialFinalWave.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TutorialHireNaked.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TutorialManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TutorialMedkit.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TutorialMinigun.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TutorialMorale.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TutorialStartMission.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TutorialStorage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TutorialWithCounter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TutorialZombieBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\TweenUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UdpUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UI\Blockers\IBlocker.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UI\Blockers\InAppUIBlocker.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UIBlocker.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UndeadBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitAbilitiesManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitAbilitiesPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitAbilityButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitDataScriptableObject.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitLevelIcon.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitMath.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitMover.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitParamPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitRandomizer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitRegister.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitReplics.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitReservedItems.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitsHeadPartsManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitsHeadsStorage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitShootingUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitsManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitsPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitState.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitTargeter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitTargetingInfo.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitTeam.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitType.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitUpgradeBalloonController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitUpgradeContent.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitUpgradeInfoConverter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitUpgradeManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitUpgradePanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnitUpgradePanelPool.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnityAds\UnityAdsFake.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnityAdsManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnityComponentExtensions.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnityEngine\UI\RectMask2DManual.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnityObjectExtensions.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnscriptedNoiseStep.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UnscriptedStep.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UpgradeableContent.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UpgradeableMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UpgradeItemSlot.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\UpgradeNotification.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Utils\Cheat.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Utils\GameCommon.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Utils\GameUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Utils\Platform.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Utils\StringUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Van.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\VectorExtensions.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\VictoryLootPresenter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\VictoryMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\VictoryMenuDebugRewards.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\VictoryMenuItemPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\VictoryVideoQuest.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\VictoryVideoRewardPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\VideoQuest.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ViewColleague.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WaitFor.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WaitingIndicator.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WallMartBattleMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WarningMessage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WarningMessageManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WarningTexts.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WasAttackedDelegate.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WatchVideoButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WaterBar.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WaterManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\Weapon.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeatherButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeatherManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WebConfig\ConfigRetriever.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WebConfig\ConfigRetrieverAsset.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WebConfig\ConfigRetrieverFile.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WebConfig\ConfigRetrieverWeb.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WebConfig\Options.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WebConfigABTest.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WebConfigLoader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WebConfigParams.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventBottomPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventData.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventGotItemsCounter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventMenuFriendsPanelsConstoller.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventMenuLight.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventMenuPanelsController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventMenuPanelsControllerBase.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventMenuXmasHead.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventQuestButton.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventReward.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventRewardMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventRewardPanel.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventRewardsDataReader.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventRewardsStorage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventWaitingIndicatorController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WeeklyEventWelcomeScreen.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WideMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WindowMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WindowSwitchMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\WitchBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\XmasBattleMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\XRayManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\YellowFatBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\YesNoMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombieBehaviour.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaAnimation.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaCamera.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaFastPagesFlip.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaFotoOffsets.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaInitInfo.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaMenu.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaPage.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaPageContent.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaPageEditorDebug.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaPageElement.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaPageGraphics.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaScrapersHolder.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaScriptable.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaSlot.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombiepediaTemplateInstance.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZombieSpawner.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZP_description.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZP_foto.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZP_horizontalLines.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZP_kills.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZP_lastPageText.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZP_linesElement.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZP_name.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZP_pageNumber.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZP_statistics.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp\ZP_unitParams.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\AchievementsStub.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\AppsFlyer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\ATAppUpdater.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Audio\AndroidAudioPlayer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Audio\IAudioPlayer.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Billing\AndroidStore.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Billing\IAndroidStoreValidator.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Billing\IStore.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Billing\StoreIOS.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\INativeAchievements.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\INativeAlert.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\INativeCrash.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Leaderboards\GameCenterStub.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Leaderboards\GooglePlayGames.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Leaderboards\IGameCenter.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Leaderboards\ILeaderboard.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Android\MRGSAdvertisingAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Android\MRGSBankAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Android\MRGSDeviceAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Android\MRGServiceAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Android\MRGSGDPRAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Android\MRGSHelper.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Android\MRGSLogAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Android\MRGSMapProxy.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Android\MRGSMetricsAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Android\MRGSMyComSupportAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Android\MRGSMyTargetAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Android\MRGSNotificationManagerAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Android\MRGSUsersAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Fake\MRGSAdvertisingFake.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Fake\MRGSBankEditor.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Fake\MRGSDeviceEditor.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Fake\MRGServiceEditor.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Fake\MRGSGDPRFake.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Fake\MRGSLogEditor.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Fake\MRGSMetricsEditor.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Fake\MRGSMyComSupportEditor.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Fake\MRGSMyTargetEditor.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Fake\MRGSNotificationManagerEditor.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\Fake\MRGSUsersEditor.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\IMRGSServerDataDelegate.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\iOS\MRGSAdvertisingiOS.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\iOS\MRGSBankiOS.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\iOS\MRGSDeviceiOS.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\iOS\MRGServiceiOS.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\iOS\MRGSGDPRiOS.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\iOS\MRGSHelper.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\iOS\MRGSLogiOS.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\iOS\MRGSMetricsiOS.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\iOS\MRGSMyComSupportiOS.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\iOS\MRGSMyTargetiOS.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\iOS\MRGSNotificationManageriOS.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\iOS\MRGSUsersiOS.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\JSON\Json.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\MRGSAdvertising.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\MRGSBank.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\MRGSBankDelegate.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\MRGSBankProduct.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\MRGSDevice.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\MRGSDoOnMainThread.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\MRGService.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\MRGSGDPR.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\MRGSLog.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\MRGSMetrics.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\MRGSMyComSupport.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\MRGSMyTarget.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\MRGSNotificationManager.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\MRGS\MRGSUsers.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\NativeAchievementsLoadedAsPercents.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\NativeAchievementsLoadedAsSteps.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\NativeAchievementsResetCompleted.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\NativeAlertAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\NativeAlertCallbackButtonClicked.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\NativeAlertDummy.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\NativeAlertWin.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\NativeCrashAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Plugins\AndroidAdvertisingId.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Plugins\IOSAdSupport.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Plugins\IOSBackgroundTasks.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Plugins\IOSLogger.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Plugins\IOSMyAppController.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Plugins\IOSNSBundle.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Plugins\IOSUIApplication.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Plugins\IOSUIScreen.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Plugins\Utils\AndroidSignChecker.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\Plugins\Utils\StringUtils.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\UnityAds\IUnityAds.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\UnityAds\UnityAdsAndroid.cs" />
     <Compile Include="Assets\Scripts\Assembly-CSharp-firstpass\UnityStandardAssets\ImageEffects\PostEffectsBase.cs" />
     <Compile Include="Assets\Suriyun\Scripts\AnimatorController.cs" />
     <Compile Include="Assets\Suriyun\Scripts\Camera Scripts\MouseOrbitImproved.cs" />
     <None Include="Assets\Resources\AssetBundles\bundles\common\ui-grayscale.shader" />
     <None Include="Assets\Suriyun\UnityChanShader\Shader\Unitychan_chara_eye.shader" />
     <None Include="Assets\OUTPUT\Plugins\VolumetricCrystalMaterials\Shaders\Volumetric.cginc" />
     <None Include="Assets\Suriyun\UnityChanShader\Shader\Unitychan_chara_fuku.shader" />
     <None Include="Assets\StreamingAssets\Version\app-version.txt" />
     <None Include="Assets\Resources\AssetBundles\bundles\common\ui-diaglines.shader" />
     <None Include="Assets\Resources\AssetBundles\battleunits3d\h_naked\CustomOutLight.shader" />
     <None Include="Assets\Resources\shaders\pixelcolor.shader" />
     <None Include="Assets\JMO Assets\Cartoon FX Remaster\CFXR Prefabs\Texts\_Make your own text effect.txt" />
     <None Include="Assets\Hovl Studio\Butterflies\Shaders\Butterfly.shader" />
     <None Include="Assets\JMO Assets\Cartoon FX (legacy)\Shaders\CFXM_MobileParticleAdd_Alpha8.shader" />
     <None Include="Assets\Resources\shaders\fade.shader" />
     <None Include="Assets\Lux Lit Particles\Shaders\Lux Lit Particles Tess Bumped.shader" />
     <None Include="Assets\Resources\AssetBundles\bundles\common\ui-uvoffsetx.shader" />
     <None Include="Assets\Resources\AssetBundles\bundles\common\mapblockedtile.shader" />
     <None Include="Assets\Hovl Studio\Toon Projectiles 2\Shaders\Distortion.shader" />
     <None Include="Assets\Hovl Studio\Sword slash VFX\Shaders\Scroll.shader" />
     <None Include="Assets\Hovl Studio\Toon Projectiles 2\Shaders\Blend_CenterGlow.shader" />
     <None Include="Assets\Lux Lit Particles\Shaders\Lux Lit Particles Bumped.shader" />
     <None Include="Assets\Hovl Studio\Sword slash VFX\Shaders\Explosion.shader" />
     <None Include="Assets\Lux Lit Particles\Lux LWRP Lit Particles README.txt" />
     <None Include="Assets\Epic Toon FX\Upgrade\URP Compatibility.txt" />
     <None Include="Assets\Hovl Studio\Butterflies\Demo scene\Readme.txt" />
     <None Include="Assets\QFX\MaterializeFX\Shaders\MFX_Standard.shader" />
     <None Include="Assets\Lux Lit Particles\Shaders\Includes\LuxParticles_Utils.cginc" />
     <None Include="Assets\Resources\shaders\mask8.shader" />
     <None Include="Assets\OUTPUT\Plugins\VolumetricCrystalMaterials\Shaders\VolumetricMobile.shader" />
     <None Include="Assets\Resources\shaders\blendvertexcolor9.shader" />
     <None Include="Assets\Hovl Studio\Sword slash VFX\Shaders\Lightning.shader" />
     <None Include="Assets\Lux Lit Particles\Shaders\Includes\LuxParticles_Core.cginc" />
     <None Include="Assets\Epic Toon FX\Prefabs\Combat\Combat FX.txt" />
     <None Include="Assets\Hovl Studio\Toon Projectiles 2\Shaders\Add_CenterGlow.shader" />
     <None Include="Assets\SoftSnow\SoftSnow - Readme.txt" />
     <None Include="Assets\QFX\MaterializeFX\Scripts Readme.txt" />
     <None Include="Assets\Resources\shaders\plane.shader" />
     <None Include="Assets\Resources\AssetBundles\bundles\battle\displacementshader.shader" />
     <None Include="Assets\Suriyun\UnityChanShader\Shader\CharaMain.cginc" />
     <None Include="Assets\Hovl Studio\Toon Projectiles 2\Shaders\DissolveNoise.shader" />
     <None Include="Assets\Epic Toon FX\Changelog.txt" />
     <None Include="Assets\AtmosphereFX\readme.txt.txt" />
     <None Include="Assets\Suriyun\UnityChanShader\Shader\Unitychan_chara_fuku_ds.shader" />
     <None Include="Assets\Hovl Studio\Sword slash VFX\Demo scene\HDRP and URP.txt" />
     <None Include="Assets\Suriyun\UnityChanShader\Shader\CharaSkin.cginc" />
     <None Include="Assets\Suriyun\UnityChanShader\Shader\Unitychan_chara_eye_blend.shader" />
     <None Include="Assets\Resources\AssetBundles\bundles\common\blendmodescoloroverlay.shader" />
     <None Include="Assets\JMO Assets\Cartoon FX Remaster\CFXR Assets\Shaders\CFXR Particle Procedural Ring.shader" />
     <None Include="Assets\JMO Assets\Cartoon FX Easy Editor\CartoonFX Easy Editor Readme.txt" />
     <None Include="Assets\OUTPUT\Plugins\VolumetricCrystalMaterials\Shaders\Volumetric.shader" />
     <None Include="Assets\Hovl Studio\Toon Projectiles 2\Demo scene\HDRP and URP(LWRP).txt" />
     <None Include="Assets\Suriyun\UnityChanShader\Shader\Unitychan_chara_hair_ds.shader" />
     <None Include="Assets\OrdossFX\PolygonalExplosions\Shaders\Polygonal_Lit.shader" />
     <None Include="Assets\QFX\MaterializeFX\Shaders Readme.txt" />
     <None Include="Assets\Suriyun\UnityChanShader\Shader\Unitychan_chara_akarami_blend.shader" />
     <None Include="Assets\QFX\MaterializeFX\Shaders\MFX_Standard Transparent.shader" />
     <None Include="Assets\Hovl Studio\Sword slash VFX\Shaders\BlendDistort.shader" />
     <None Include="Assets\Epic Toon FX\Shaders\ETFX_PowerboxUnlit.shader" />
     <None Include="Assets\Lux Lit Particles\Changelog.txt" />
     <None Include="Assets\QFX\MaterializeFX\Shaders\MFX_Unlit.shader" />
     <None Include="Assets\Suriyun\UnityChanShader\Shader\Unitychan_chara_hair.shader" />
     <None Include="Assets\Resources\AssetBundles\bundles\common\ui-lerpcolor.shader" />
     <None Include="Assets\Resources\shaders\mask.shader" />
     <None Include="Assets\Suriyun\UnityChanShader\Shader\Unitychan_chara_eyelash_blend.shader" />
     <None Include="Assets\Resources\AssetBundles\bundles\common\ui-hue.shader" />
     <None Include="Assets\Resources\shaders\white.shader" />
     <None Include="Assets\JMO Assets\Cartoon FX (legacy)\Demo\Assets\CFX_Demo_WaterWiggle.shader" />
     <None Include="Assets\Epic Toon FX\Prefabs\Interactive\Interactive FX.txt" />
     <None Include="Assets\Suriyun\UnityChanShader\Shader\CharaOutline.cginc" />
     <None Include="Assets\JMO Assets\Cartoon FX (legacy)\Shaders\CFXM_MobileParticleAB_Alpha8.shader" />
     <None Include="Assets\JMO Assets\Cartoon FX Remaster\CFXR Assets\Shaders\CFXR Particle Glow.shader" />
     <None Include="Assets\Resources\AssetBundles\bundles\common\sprites-lerpcolor.shader" />
     <None Include="Assets\QFX\MaterializeFX\Readme.txt" />
     <None Include="Assets\link.xml" />
     <None Include="Assets\Suriyun\UnityChanShader\Shader\readme.txt" />
     <None Include="Assets\Resources\AssetBundles\bundles\common\ui-horizlines.shader" />
     <None Include="Assets\Suriyun\UnityChanShader\Shader\Unitychan_chara_hada.shader" />
     <None Include="Assets\Lux Lit Particles\Demos\Fonts\ZwriteFont LLP.shader" />
     <None Include="Assets\Hovl Studio\Toon Projectiles 2\Shaders\Blend_TwoSides.shader" />
     <None Include="Assets\QFX\MaterializeFX\Change Log.txt" />
     <None Include="Assets\JMO Assets\Cartoon FX Remaster\CFXR Assets\Shaders\CFXR.cginc" />
     <None Include="Assets\Hovl Studio\Toon Projectiles 2\Demo scene\Readme.txt" />
     <None Include="Assets\OUTPUT\Plugins\VolumetricCrystalMaterials\ExampleScene\Floor.shader" />
     <None Include="Assets\Hovl Studio\Sword slash VFX\Shaders\SwordSlash.shader" />
     <None Include="Assets\Suriyun\UnityChanShader\Shader\Unitychan_chara_hada_blend.shader" />
     <None Include="Assets\JMO Assets\Cartoon FX Remaster\Readme Cartoon FX Remaster.txt" />
     <None Include="Assets\OrdossFX\PolygonalExplosions\readme.txt" />
     <None Include="Assets\Suriyun\README.txt" />
     <None Include="Assets\Resources\AssetBundles\bundles\common\ui-default-notexture.shader" />
     <None Include="Assets\Hovl Studio\Sword slash VFX\Shaders\Ice.shader" />
     <None Include="Assets\Lana Studio\Casual RPG VFX\Readme.txt" />
     <None Include="Assets\JMO Assets\Cartoon FX Remaster\CFXR Assets\Shaders\CFXR Particle Ubershader.shader" />
     <None Include="Assets\Resources\shaders\rimlight.shader" />
     <None Include="Assets\Lux Lit Particles\Shaders\Includes\UnityStandardShadowLike.cginc" />
     <None Include="Assets\Hovl Studio\Sword slash VFX\Shaders\Add_Fresnel.shader" />
     <None Include="Assets\Lux Lit Particles\Shaders\Includes\LuxParticles_Tess.cginc" />
     <None Include="Assets\Resources\shaders\multiply.shader" />
     <None Include="Assets\OrdossFX\PolygonalExplosions\Shaders\Polygonal_BlendHDR.shader" />
     <None Include="Assets\Resources\AssetBundles\bundles\common\ui-zombiepediaphoto.shader" />
     <None Include="Assets\Resources\AssetBundles\bundles\common\ui-grayscalecolored.shader" />
     <None Include="Assets\QFX\MaterializeFX\Shaders\MFX_Standard Specular.shader" />
     <None Include="Assets\Epic Toon FX\Prefabs\Environment\Environment FX.txt" />
     <None Include="Assets\Resources\AssetBundles\bundles\common\sprites-gradient.shader" />
     <None Include="Assets\Hovl Studio\Sword slash VFX\Demo scene\Readme.txt" />
     <None Include="Assets\Resources\shaders\grayscale.shader" />
     <None Include="Assets\JMO Assets\Cartoon FX (legacy)\CartoonFX Readme.txt" />
     <None Include="Assets\Hovl Studio\Butterflies\Prefabs\How to use.txt" />
     <None Include="Assets\Resources\AssetBundles\battleunits3d\h_snaked\CustomOutLight.shader" />
     <None Include="Assets\Epic Toon FX\Shaders\ETFX_PowerboxLit.shader" />
 <Reference Include="UnityEngine.Purchasing">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Library/ScriptAssemblies/UnityEngine.Purchasing.dll</HintPath>
 </Reference>
 <Reference Include="UnityEditor.Purchasing">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Library/ScriptAssemblies/UnityEditor.Purchasing.dll</HintPath>
 </Reference>
 <Reference Include="Unity.TextMeshPro.Editor">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.PackageManagerUI.Editor">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Library/ScriptAssemblies/Unity.PackageManagerUI.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.CollabProxy.Editor">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll</HintPath>
 </Reference>
 <Reference Include="Unity.TextMeshPro">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Library/ScriptAssemblies/Unity.TextMeshPro.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Analytics.DataPrivacy">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Library/ScriptAssemblies/Unity.Analytics.DataPrivacy.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AIModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ARModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AccessibilityModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AnimationModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AssetBundleModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.AudioModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.BaselibModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.BaselibModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ClothModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ClusterInputModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ClusterRendererModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.CoreModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.CrashReportingModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.DirectorModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.FileSystemHttpModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.FileSystemHttpModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.GameCenterModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.GridModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.HotReloadModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.IMGUIModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ImageConversionModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.InputModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.JSONSerializeModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.LocalizationModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ParticleSystemModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.PerformanceReportingModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.PhysicsModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.Physics2DModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ProfilerModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.ScreenCaptureModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SharedInternalsModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SpriteMaskModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SpriteShapeModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.StreamingModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.StyleSheetsModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.StyleSheetsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SubstanceModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TLSModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TerrainModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TerrainPhysicsModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TextCoreModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TextRenderingModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TilemapModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TimelineModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.TimelineModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UIModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UIElementsModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UNETModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UmbraModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityAnalyticsModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityConnectModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityTestProtocolModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestAudioModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestTextureModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UnityWebRequestWWWModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VFXModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VRModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VehiclesModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.VideoModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.WindModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.XRModule">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Locator">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/Managed/Unity.Locator.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.UI">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/GUISystem/UnityEngine.UI.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.TestRunner">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/TestRunner/UnityEngine.TestRunner.dll</HintPath>
 </Reference>
 <Reference Include="nunit.framework">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/TestRunner/net35/unity-custom/nunit.framework.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.Timeline">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/Timeline/RuntimeEditor/UnityEngine.Timeline.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.Networking">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/Networking/UnityEngine.Networking.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.GoogleAudioSpatializer">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/UnityGoogleAudioSpatializer/RuntimeEditor/UnityEngine.GoogleAudioSpatializer.dll</HintPath>
 </Reference>
 <Reference Include="UnityEngine.SpatialTracking">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/UnityExtensions/Unity/UnitySpatialTracking/RuntimeEditor/UnityEngine.SpatialTracking.dll</HintPath>
 </Reference>
 <Reference Include="AWSSDK.CognitoIdentity">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Assets/Plugins/AWSSDK.CognitoIdentity.dll</HintPath>
 </Reference>
 <Reference Include="AWSSDK.CognitoSync">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Assets/Plugins/AWSSDK.CognitoSync.dll</HintPath>
 </Reference>
 <Reference Include="AWSSDK.Core">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Assets/Plugins/AWSSDK.Core.dll</HintPath>
 </Reference>
 <Reference Include="AWSSDK.KinesisFirehose">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Assets/Plugins/AWSSDK.KinesisFirehose.dll</HintPath>
 </Reference>
 <Reference Include="AWSSDK.SecurityToken">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Assets/Plugins/AWSSDK.SecurityToken.dll</HintPath>
 </Reference>
 <Reference Include="CSSDK">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Assets/Plugins/CSSDK.dll</HintPath>
 </Reference>
 <Reference Include="DOTween">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Assets/Plugins/DOTween.dll</HintPath>
 </Reference>
 <Reference Include="DOTween46">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Assets/Plugins/DOTween46.dll</HintPath>
 </Reference>
 <Reference Include="DOTween50">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Assets/Plugins/DOTween50.dll</HintPath>
 </Reference>
 <Reference Include="ICSharpCode.SharpZipLib">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Assets/Plugins/ICSharpCode.SharpZipLib.dll</HintPath>
 </Reference>
 <Reference Include="Mono.Data.Sqlite">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Assets/Plugins/Mono.Data.Sqlite.dll</HintPath>
 </Reference>
 <Reference Include="Newtonsoft.Json">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Assets/Plugins/Newtonsoft.Json.dll</HintPath>
 </Reference>
 <Reference Include="Unity.Analytics.StandardEvents">
 <HintPath>D:/titan/work/projects/Lili/DeadAheadZombie/trunk/Proj_DeadAheadZombie/Library/PackageCache/com.unity.analytics@3.2.3/AnalyticsStandardEvents/Unity.Analytics.StandardEvents.dll</HintPath>
 </Reference>
 <Reference Include="mscorlib">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll</HintPath>
 </Reference>
 <Reference Include="System">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll</HintPath>
 </Reference>
 <Reference Include="System.Core">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.Linq">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll</HintPath>
 </Reference>
 <Reference Include="System.Numerics">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll</HintPath>
 </Reference>
 <Reference Include="System.Numerics.Vectors">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Http">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.CSharp">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll</HintPath>
 </Reference>
 <Reference Include="System.Data">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll</HintPath>
 </Reference>
 <Reference Include="Microsoft.Win32.Primitives">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="netstandard">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll</HintPath>
 </Reference>
 <Reference Include="System.AppContext">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.Concurrent">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.NonGeneric">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll</HintPath>
 </Reference>
 <Reference Include="System.Collections.Specialized">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.Annotations">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.EventBasedAsync">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.Primitives">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.ComponentModel.TypeConverter">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll</HintPath>
 </Reference>
 <Reference Include="System.Console">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll</HintPath>
 </Reference>
 <Reference Include="System.Data.Common">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Contracts">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Debug">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.FileVersionInfo">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Process">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.StackTrace">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.TextWriterTraceListener">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.Tools">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll</HintPath>
 </Reference>
 <Reference Include="System.Diagnostics.TraceSource">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll</HintPath>
 </Reference>
 <Reference Include="System.Drawing.Primitives">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Dynamic.Runtime">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization.Calendars">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll</HintPath>
 </Reference>
 <Reference Include="System.Globalization.Extensions">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.Compression.ZipFile">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll</HintPath>
 </Reference>
 <Reference Include="System.IO">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.DriveInfo">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.Primitives">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.FileSystem.Watcher">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.IsolatedStorage">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.MemoryMappedFiles">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.Pipes">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll</HintPath>
 </Reference>
 <Reference Include="System.IO.UnmanagedMemoryStream">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Expressions">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Parallel">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll</HintPath>
 </Reference>
 <Reference Include="System.Linq.Queryable">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Http.Rtc">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.NameResolution">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.NetworkInformation">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Ping">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Primitives">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Requests">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Security">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.Sockets">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebHeaderCollection">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebSockets.Client">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll</HintPath>
 </Reference>
 <Reference Include="System.Net.WebSockets">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll</HintPath>
 </Reference>
 <Reference Include="System.ObjectModel">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit.ILGeneration">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Emit.Lightweight">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Extensions">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Reflection.Primitives">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.Reader">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.ResourceManager">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll</HintPath>
 </Reference>
 <Reference Include="System.Resources.Writer">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.CompilerServices.VisualC">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Extensions">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Handles">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Numerics">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Formatters">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Json">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Primitives">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Runtime.Serialization.Xml">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Claims">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Algorithms">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Csp">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Encoding">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.Primitives">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Cryptography.X509Certificates">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.Principal">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll</HintPath>
 </Reference>
 <Reference Include="System.Security.SecureString">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Duplex">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Http">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.NetTcp">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Primitives">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll</HintPath>
 </Reference>
 <Reference Include="System.ServiceModel.Security">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.Encoding">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.Encoding.Extensions">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll</HintPath>
 </Reference>
 <Reference Include="System.Text.RegularExpressions">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Overlapped">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Tasks">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Tasks.Parallel">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Thread">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.ThreadPool">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll</HintPath>
 </Reference>
 <Reference Include="System.Threading.Timer">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll</HintPath>
 </Reference>
 <Reference Include="System.ValueTuple">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.ReaderWriter">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XDocument">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XmlDocument">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XmlSerializer">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XPath">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll</HintPath>
 </Reference>
 <Reference Include="System.Xml.XPath.XDocument">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll</HintPath>
 </Reference>
 <Reference Include="UnityScript">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/unityscript/UnityScript.dll</HintPath>
 </Reference>
 <Reference Include="UnityScript.Lang">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/unityscript/UnityScript.Lang.dll</HintPath>
 </Reference>
 <Reference Include="Boo.Lang">
 <HintPath>C:/Program Files/Unity/2018.4.33f1/Editor/Data/MonoBleedingEdge/lib/mono/unityscript/Boo.Lang.dll</HintPath>
 </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="CFXRRuntime.csproj">
      <Project>{035A08B6-77E9-6856-61FE-7089067D0C1D}</Project>      <Name>CFXRRuntime</Name>    </ProjectReference>
    <ProjectReference Include="CFXREditor.csproj">
      <Project>{017D4F48-5AF7-DE5B-3367-DA6499E72ACD}</Project>      <Name>CFXREditor</Name>    </ProjectReference>
    <ProjectReference Include="CFXRDemo.csproj">
      <Project>{12918621-183C-B69B-97FF-0174870F2CD2}</Project>      <Name>CFXRDemo</Name>    </ProjectReference>
    <ProjectReference Include="AllIn1VfxDemoScriptAssemblies.csproj">
      <Project>{FDE47B5D-77CA-BA5F-70C1-BB4EC3C584F0}</Project>      <Name>AllIn1VfxDemoScriptAssemblies</Name>    </ProjectReference>
    <ProjectReference Include="FR2.csproj">
      <Project>{6A89E791-ACB0-0817-455E-2FA413FD447C}</Project>      <Name>FR2</Name>    </ProjectReference>
    <ProjectReference Include="AllIn1SpriteShaderAssembly.csproj">
      <Project>{BC9E5489-04B8-4FCD-FF9C-6F32040D98C9}</Project>      <Name>AllIn1SpriteShaderAssembly</Name>    </ProjectReference>
    <ProjectReference Include="AllIn1VfxAssmebly.csproj">
      <Project>{DDD902AF-78D1-0E52-1303-F086820F5357}</Project>      <Name>AllIn1VfxAssmebly</Name>    </ProjectReference>
    <ProjectReference Include="CFXRDemoEditor.csproj">
      <Project>{5E9DA290-0749-0087-FC6B-AE2539578737}</Project>      <Name>CFXRDemoEditor</Name>    </ProjectReference>
    <ProjectReference Include="AllIn1VfxTexDemoAssembly.csproj">
      <Project>{406E34F9-C896-F5DA-9908-5093D33BE97B}</Project>      <Name>AllIn1VfxTexDemoAssembly</Name>    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  
</Project>
