# CorpseType 配置数据使用说明

## 概述

现在你可以在游戏中使用 `primarydata.2.6.2.json` 文件中的 `CorpseType` 数据了。这个功能允许你为不同的单位类型指定死亡后显示的尸体类型。

## 配置文件格式

在 `primarydata.2.6.2.json` 文件中，`CorpseType` 数据格式如下：

```json
"CorpseType":{
  "h_farmer":"z_farmer",
  "h_mechanic":"z_mechanic"
}
```

这表示：
- 农民 (`h_farmer`) 死亡后显示僵尸农民 (`z_farmer`) 的尸体
- 机械师 (`h_mechanic`) 死亡后显示僵尸机械师 (`z_mechanic`) 的尸体

## 代码实现

### 1. BattleParams 类的增强

在 `BattleParams` 类中添加了以下功能：

- `CorpseTypeMapping` 属性：存储尸体类型映射字典
- `LoadCorpseTypeMapping()` 方法：从配置文件加载映射数据
- `GetCorpseType()` 方法：获取指定单位的尸体类型

### 2. FxCreatureCorpse 类的修改

修改了 `Init()` 方法，现在会自动使用配置中的尸体类型映射：

```csharp
public void Init(CreatureTemplate.CreatureType corpseType, float corpseLifeTime, float flip)
{
    // 使用配置中的尸体类型映射
    CreatureTemplate.CreatureType actualCorpseType = BattleParams.Instance.GetCorpseType(corpseType);
    
    CreatureTemplate.ApplyAnimOffsetY(actualCorpseType, spriteRenderer.transform);
    StartCoroutine(Coroutine(actualCorpseType, corpseLifeTime, flip));
}
```

## 使用方法

### 基本用法

```csharp
// 获取农民死亡后的尸体类型
CreatureTemplate.CreatureType corpseType = BattleParams.Instance.GetCorpseType(CreatureTemplate.CreatureType.h_farmer);
// 结果：CreatureTemplate.CreatureType.z_farmer

// 获取没有映射的单位的尸体类型（返回原类型）
CreatureTemplate.CreatureType nakedCorpseType = BattleParams.Instance.GetCorpseType(CreatureTemplate.CreatureType.h_naked);
// 结果：CreatureTemplate.CreatureType.h_naked
```

### 检查是否有特殊映射

```csharp
public bool HasSpecialCorpseType(CreatureTemplate.CreatureType unitType)
{
    CreatureTemplate.CreatureType corpseType = BattleParams.Instance.GetCorpseType(unitType);
    return corpseType != unitType; // 如果尸体类型与原单位类型不同，说明有特殊映射
}
```

### 在单位死亡时使用

```csharp
public void OnUnitDeath(CreatureTemplate.CreatureType deadUnitType)
{
    // 获取正确的尸体类型
    CreatureTemplate.CreatureType corpseType = BattleParams.Instance.GetCorpseType(deadUnitType);
    
    // 创建尸体时使用映射后的类型
    // FxCreatureCorpse 会自动处理这个映射
}
```

## 自动加载

系统会在游戏启动时自动加载配置数据，你不需要手动调用任何加载方法。配置数据通过 `WebConfigLoader` 系统自动加载。

## 调试信息

当配置加载时，控制台会显示调试信息：

```
加载尸体类型映射: h_farmer -> z_farmer
加载尸体类型映射: h_mechanic -> z_mechanic
```

如果配置中有无效的单位类型，会显示警告：

```
无法解析尸体类型映射: invalid_unit -> another_invalid_unit
```

## 注意事项

1. 如果配置文件中没有 `CorpseType` 数据，系统会正常工作，所有单位使用默认的尸体类型（即原单位类型）
2. 如果映射的目标单位类型不存在，会显示警告但不会影响游戏运行
3. 系统会自动处理大小写敏感的单位类型名称
4. `FxCreatureCorpse` 现在会自动使用映射后的尸体类型，无需修改现有的尸体生成代码

## 示例代码

参考 `CorpseTypeExample.cs` 文件中的完整示例代码。
