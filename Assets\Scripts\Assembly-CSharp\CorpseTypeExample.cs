using UnityEngine;

/// <summary>
/// 示例类：展示如何使用 CorpseType 配置数据
/// </summary>
public class CorpseTypeExample : MonoBehaviour
{
    /// <summary>
    /// 测试方法：展示如何获取单位的尸体类型
    /// </summary>
    public void TestCorpseTypeMapping()
    {
        // 测试农民 (h_farmer) 的尸体类型
        CreatureTemplate.CreatureType farmerCorpseType = BattleParams.Instance.GetCorpseType(CreatureTemplate.CreatureType.h_farmer);
        Debug.Log($"农民死亡后的尸体类型: {farmerCorpseType}");
        
        // 测试机械师 (h_mechanic) 的尸体类型
        CreatureTemplate.CreatureType mechanicCorpseType = BattleParams.Instance.GetCorpseType(CreatureTemplate.CreatureType.h_mechanic);
        Debug.Log($"机械师死亡后的尸体类型: {mechanicCorpseType}");
        
        // 测试没有映射的单位类型
        CreatureTemplate.CreatureType nakedCorpseType = BattleParams.Instance.GetCorpseType(CreatureTemplate.CreatureType.h_naked);
        Debug.Log($"裸体人死亡后的尸体类型: {nakedCorpseType}");
    }
    
    /// <summary>
    /// 在游戏中实际使用的示例：当单位死亡时获取正确的尸体类型
    /// </summary>
    /// <param name="deadUnit">死亡的单位类型</param>
    /// <returns>应该显示的尸体类型</returns>
    public CreatureTemplate.CreatureType GetCorpseTypeForDeadUnit(CreatureTemplate.CreatureType deadUnit)
    {
        return BattleParams.Instance.GetCorpseType(deadUnit);
    }
    
    /// <summary>
    /// 检查某个单位是否有特殊的尸体类型映射
    /// </summary>
    /// <param name="unitType">要检查的单位类型</param>
    /// <returns>如果有特殊映射返回 true，否则返回 false</returns>
    public bool HasSpecialCorpseType(CreatureTemplate.CreatureType unitType)
    {
        CreatureTemplate.CreatureType corpseType = BattleParams.Instance.GetCorpseType(unitType);
        return corpseType != unitType; // 如果尸体类型与原单位类型不同，说明有特殊映射
    }
}
