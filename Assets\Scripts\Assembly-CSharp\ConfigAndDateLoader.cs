using System;
using System.Collections;
using System.Threading;
using SaveGame;
using UnityEngine;

public class ConfigAndDateLoader
{
	public enum LoadOperation
	{
		None,
		InProgress,
		Success,
		Failed
	}

	private static ConfigAndDateLoader instance = new ConfigAndDateLoader();

	private bool didRaiseLoadedEvent;

	private DateTime prevRaisedDate = DateTime.MaxValue;

	private DateTime ntpBasedDate = DateTime.MaxValue;

	private DateTime ntpBasedDateNotPausable = DateTime.MaxValue;

	private bool isDatePaused = true;

	private int mainThreadId;

	private DateTime lastForceRequestDateDate = DateTime.MinValue;

	public static ConfigAndDateLoader Instance
	{
		get
		{
			return instance;
		}
	}

	public bool IsConfigLoaded { get; private set; }

	public bool IsL10nLoaded { get; private set; }

	public bool IsDateLoadedOnce { get; private set; }

	public LoadOperation DateLoadStatus { get; private set; }

	public LoadOperation AllLoadStatus { get; private set; }

	public DateTime DateWhenForceRequestDate { get; set; }

	public DateTime NTPBasedDate
	{
		get
		{
			if (!IsDateLoadedOnce)
			{
				throw new InvalidOperationException("ConfigAndDateLoader.NTPBasedDate, date is not loaded");
			}
			return ntpBasedDate;
		}
		 set
		{
			ntpBasedDate = value;
			IsDateLoadedOnce = true;
		}
	}

	public DateTime NTPBasedDateNotPausable
	{
		get
		{
			if (Thread.CurrentThread.ManagedThreadId != mainThreadId)
			{
				throw new InvalidOperationException("ConfigAndDateLoader.NTPBasedDateNotPausable, not main thread");
			}
			if (!IsDateLoadedOnce)
			{
				throw new InvalidOperationException("ConfigAndDateLoader.NTPBasedDateNotPausable, date is not loaded");
			}
			return ntpBasedDateNotPausable;
		}
		private set
		{
			ntpBasedDateNotPausable = value;
		}
	}

	public TimeSpan DeltaTime { get; internal set; }

	public bool IsDatePaused
	{
		get
		{
			return isDatePaused;
		}
		set
		{
			if (isDatePaused != value)
			{
				isDatePaused = value;
				if (value)
				{
					this.NTPDatePaused();
				}
				else
				{
					this.NTPDateResumed();
				}
			}
		}
	}

	public event Action Loaded = delegate
	{
	};

	public event Action<DateTime> NTPDateChanged = delegate
	{
	};

	public event Action NTPDatePaused = delegate
	{
	};

	public event Action NTPDateResumed = delegate
	{
	};

	public event Action GameUpdated = delegate
	{
	};

	private ConfigAndDateLoader()
	{
		mainThreadId = Thread.CurrentThread.ManagedThreadId;
		DeltaTime = TimeSpan.Zero;
	}

	public void Init()
	{
	}

	public void BeginLoading(Action onNTPDateRequestFinished, Action onNTPRetry)
	{
        Debug.LogError("BeginLoading");
		if (AllLoadStatus != LoadOperation.InProgress)
		{
			Debug.Log("ConfigAndDateLoader.BeginLoading");
			Game.Instance.StartCoroutine(CoLoadAll(onNTPDateRequestFinished, onNTPRetry));
		}
	}

	private IEnumerator CoLoadAll(Action onNTPDateRequestFinished, Action onNTPRetry)
	{
		AllLoadStatus = LoadOperation.InProgress;
		int completeCounter = 2;
        //Debug.LogError("CoLoadAll");
		Game.Instance.StartCoroutine(CoLoadDate(onNTPRetry, delegate
		{
            //Debug.LogError("CoLoadDate");

			if (onNTPDateRequestFinished != null)
			{
				onNTPDateRequestFinished();
			}
			completeCounter--;
		}));
        Game.Instance.StartCoroutine(CoLoadConfigAndL10n(delegate
        {
            // Debug.LogError("CoLoadConfigAndL10n");
            completeCounter--;
        }));
        while (completeCounter > 0)
		{
			yield return null;
		}
       // Debug.LogError(DateLoadStatus+" "+ IsConfigLoaded+" "+ IsL10nLoaded);
		Finish((DateLoadStatus != LoadOperation.Success || !IsConfigLoaded || !IsL10nLoaded) ? LoadOperation.Failed : LoadOperation.Success);
	}

	private IEnumerator CoLoadConfigAndL10n(Action onComplete)
	{
		if (IsConfigLoaded && IsL10nLoaded)
		{
			onComplete();
			yield break;
		}
		if (!Game.IsProduction)
		{
			IsL10nLoaded = true;
		}
		if (!IsConfigLoaded)
		{
			bool applyingConfigFinished = false;
			bool isApplySuccess = false;
			WebConfigLoader.Instance.ApplyConfig(delegate(bool success)
			{
				applyingConfigFinished = true;
				isApplySuccess = success;
			});
			while (!applyingConfigFinished)
			{
				yield return null;
			}
			if (!isApplySuccess)
			{
				onComplete();
				yield break;
			}
			IsConfigLoaded = true;
		}
		if (!IsL10nLoaded)
		{
			bool isL10nFinished = false;
			bool isL10nSuccess = false;
			LocalizationManager.Instance.DownloadAndApplyNewL10n(false, delegate(bool success, string msg)
			{
				isL10nFinished = true;
				isL10nSuccess = success;
			});
			while (!isL10nFinished)
			{
				yield return null;
			}
			if (isL10nSuccess)
			{
				IsL10nLoaded = true;
			}
		}
		onComplete();
	}

	private IEnumerator CoLoadDate(Action onNTPRetry, Action onComplete)
	{

        
		DateLoadStatus = LoadOperation.InProgress;
		yield return NTP.Instance.CoRequestDate(onNTPRetry);
		DateLoadStatus = ((/*!NTP.Instance.IsSuccess*/false) ? LoadOperation.Failed : LoadOperation.Success);
		if (DateLoadStatus == LoadOperation.Success)
		{
			DateTime dateTime2 = (NTPBasedDateNotPausable = (NTPBasedDate = /*NTP.Instance.Date*/DateTime.UtcNow));
		}
		onComplete();
	}

	private void Finish(LoadOperation newStatus)
	{
		AllLoadStatus = newStatus;
		if (newStatus != LoadOperation.Success)
		{
			return;
		}
		if (!didRaiseLoadedEvent)
		{
			didRaiseLoadedEvent = true;
			Migration.Instance.ReadData();
			if (!SaveGameService.Instance.LoadUserData())
			{
				AllLoadStatus = LoadOperation.InProgress;
				return;
			}
			Debug.Log("ConfigAndDateLoader, user data loaded");
			NTP.DateChanged += OnNTPDateChanged;
			Game.Instance.Updated += OnGameUpdated;
			Game.Instance.ApplicationPaused += OnApplicationPaused;
			this.Loaded();
		}
		if (!Game.Instance.IsPaused)
		{
			IsDatePaused = false;
			RaiseDateChanged();
		}
	}

	public float GetProgress()
	{
		if (!IsConfigLoaded)
		{
			return WebConfigLoader.Instance.GetProgress();
		}
		if (!IsL10nLoaded)
		{
			return LocalizationManager.Instance.DownloadProgress;
		}
		if (DateLoadStatus == LoadOperation.InProgress)
		{
			return 0.9f;
		}
		return 1f;
	}

	private void OnApplicationPaused()
	{
		IsDatePaused = true;
	}

	private void OnNTPDateChanged(DateTime date)
	{
		if (AllLoadStatus != LoadOperation.InProgress)
		{
			NTPBasedDate = date;
			NTPBasedDateNotPausable = date;
			if (!Game.Instance.IsPaused)
			{
				IsDatePaused = false;
				RaiseDateChanged();
			}
		}
	}

	private void OnGameUpdated()
	{
		float unscaledDeltaTime = Time.unscaledDeltaTime;
		TimeSpan timeSpan = TimeSpan.FromSeconds(unscaledDeltaTime);
		ntpBasedDateNotPausable += timeSpan;
		if (AllLoadStatus != LoadOperation.InProgress && !IsDatePaused)
		{
			DateTime dateTime = ntpBasedDate + timeSpan;
			if (ntpBasedDate < DateWhenForceRequestDate && dateTime >= DateWhenForceRequestDate && unscaledDeltaTime < 1f && (dateTime - lastForceRequestDateDate).Duration().TotalSeconds >= 10.0)
			{
				lastForceRequestDateDate = dateTime;
				DateWhenForceRequestDate = DateTime.MinValue;
				NTP.Instance.RequestDate();
			}
			ntpBasedDate = dateTime;
			RaiseDateChanged();
		}
		this.GameUpdated();
	}

	private void RaiseDateChanged()
	{
		DateTime nTPBasedDate = NTPBasedDate;
		DeltaTime = nTPBasedDate - prevRaisedDate;
		if (DeltaTime.Ticks < 0)
		{
			DeltaTime = TimeSpan.Zero;
		}
		this.NTPDateChanged(nTPBasedDate);
		prevRaisedDate = nTPBasedDate;
	}
}
