using System;
using System.Collections.Generic;
using System.Globalization;
using UnityEngine;

public sealed class BattleParams : CommonReader
{
	private static BattleParams instance = new BattleParams();

	public static BattleParams Instance
	{
		get
		{
			return instance;
		}
	}

	public string HumanStartGroup { get; private set; }

	public string ZombieStartGroup { get; private set; }

	public string ZombieSpawnDensity { get; private set; }

	public bool FriendlyFireOn { get; private set; }

	public bool MoraleOn { get; private set; }

	public bool RandomAgility { get; private set; }

	public bool RandomAggression { get; private set; }

	public bool RandomMoveSpeed { get; private set; }

	public bool RandomCritChance { get; private set; }

	public bool SpawnAfterDeathOn { get; private set; }

	public int ZombieResurrectHPPercent { get; private set; }

	public string NoiseZombieDeck { get; private set; }

	public int NoiseReduceSpeed { get; private set; }

	public int NoiseCooldownSec { get; private set; }

	public int WaterGainSpeedPer10Seconds { get; private set; }

	public int BonusZombieRage { get; private set; }

	public int BonusZombieRageChance { get; private set; }

	public int StartingManaResource { get; private set; }

	public int StartingRageResource { get; private set; }

	public float StartingCooldownSkipSeconds { get; private set; }

	public int ShieldSize { get; private set; }

	public int ShieldRadius { get; private set; }

	public int DemonBonusDmg { get; private set; }

	public int WaterBuffDropAmount { get; private set; }

	public float EliteHumanChance { get; private set; }

	public int BanditTruckMissionId { get; private set; }

	public int BanditTruckAttackCooldown { get; private set; }

	public string RandomSpawnZombies { get; private set; }

	public float GrenaderGrenadeCooldownSeconds { get; private set; }

	public float QueenGrenadeCooldownSeconds { get; private set; }

	public float UnitFreezeDebuffTime { get; private set; }

	public int GrenaderGrenadeDamage { get; private set; }

	public float PsyPushSpeed { get; private set; }

	public float PsyMaxPushDistance { get; private set; }

	public float PsyPushForce { get; private set; }

	public OnDeadConversion DeadConversion { get; private set; }

	public float TimeToAutoHideWater { get; private set; }

	public float ImmortalTimeAfterResurrectSeconds { get; private set; }

	public float RandomPushResist { get; private set; }

	public int AlienCopRestoreHP { get; private set; }

	public int BillManualShootTapRadius { get; private set; }

	public int InsectJumpDamage { get; private set; }

	public Vector3 InsectJumpColliderSize { get; private set; }

	public float EggChargeCooldown { get; private set; }

	public float TurretRechargeTime { get; private set; }

	public int TurretOnReloadDamage { get; private set; }

	public int GeneratorOnSpawnDamage { get; private set; }

	public int BuilderRunSelfDamage { get; private set; }

	public int BuilderRunSpecialSpeed { get; private set; }

	public int MedkitHealCount { get; private set; }

	public float MedkitHealDelaySec { get; private set; }

	public int PVPReplicsCooldown { get; private set; }

	public float CephalopodaSimpleAttackCooldown { get; private set; }

	public int CephalopodaSimpleAttackDamage { get; private set; }

	public int CephalopodaFinalStrikeCooldown { get; private set; }

	public int CephalopodaUltDamage { get; private set; }

	public int CephalopodaFireRepeatDamage { get; private set; }

	public string AustinBattleLoot { get; private set; }

	public int SwatFireChance { get; private set; }

	public int SwatFireDamage { get; private set; }

	public float ChopperPercentForHeal { get; private set; }

	public float ChopperHealCooldown { get; private set; }

	public float CarolJumpRangeSqrMagnitude { get; private set; }

	public Dictionary<CreatureTemplate.CreatureType, CreatureTemplate.CreatureType> CorpseTypeMapping { get; private set; }

	private BattleParams()
		: base("battleparam_")
	{
	}

	public override void ClearLoadedItems()
	{
	}

	public override bool LoadItems(Dictionary<string, object> dict)
	{
		HumanStartGroup = (string)GetPrefixedValue(dict, "humanStartGroup", string.Empty);
		ZombieStartGroup = (string)GetPrefixedValue(dict, "zombieStartGroup", string.Empty, false);
		ZombieSpawnDensity = (string)GetPrefixedValue(dict, "zombieSpawnDensity", string.Empty);
		FriendlyFireOn = (bool)GetPrefixedValue(dict, "friendlyFireOn", true);
		MoraleOn = (bool)GetPrefixedValue(dict, "moraleOn", true);
		RandomAgility = (bool)GetPrefixedValue(dict, "randomAgility", true);
		RandomAggression = (bool)GetPrefixedValue(dict, "randomAggression", true);
		RandomMoveSpeed = (bool)GetPrefixedValue(dict, "randomMoveSpeed", true);
		RandomCritChance = (bool)GetPrefixedValue(dict, "randomCritChance", true);
		SpawnAfterDeathOn = (bool)GetPrefixedValue(dict, "spawnAfterDeathOn", true);
		ZombieResurrectHPPercent = GetPrefixedIntValue(dict, "zombieResurrectHPPercent", 100);
		NoiseZombieDeck = (string)GetPrefixedValue(dict, "noiseZombieDeck", string.Empty, false);
		NoiseReduceSpeed = GetPrefixedIntValue(dict, "noiseReduceSpeed", 100);
		NoiseCooldownSec = GetPrefixedIntValue(dict, "noiseCooldownSec", 0);
		WaterGainSpeedPer10Seconds = GetPrefixedIntValue(dict, "waterGainSpeedPer10Seconds", 0);
		BonusZombieRage = GetPrefixedIntValue(dict, "bonusZombieRage", 40);
		BonusZombieRageChance = GetPrefixedIntValue(dict, "bonusZombieRageChance", 10);
		StartingManaResource = GetPrefixedIntValue(dict, "startingManaResource", 0);
		StartingRageResource = GetPrefixedIntValue(dict, "startingRageResource", 0);
		StartingCooldownSkipSeconds = Convert.ToSingle(GetPrefixedValue(dict, "startingCooldownSkipSeconds", 0f));
		ShieldSize = GetPrefixedIntValue(dict, "ShieldSize", 1);
		ShieldRadius = GetPrefixedIntValue(dict, "ShieldRadius", 1);
		DemonBonusDmg = GetPrefixedIntValue(dict, "demonBonusDmg", 1);
		WaterBuffDropAmount = GetPrefixedIntValue(dict, "waterBuffDropAmount", 1);
		EliteHumanChance = Convert.ToSingle(GetPrefixedValue(dict, "eliteHumanChance", 1f));
		BanditTruckMissionId = GetPrefixedIntValue(dict, "banditTruckMissionId", -1);
		BanditTruckAttackCooldown = GetPrefixedIntValue(dict, "banditTruckAttackCooldown", 3);
		RandomSpawnZombies = (string)GetPrefixedValue(dict, "randomSpawnZombies", string.Empty);
		GrenaderGrenadeCooldownSeconds = Convert.ToSingle(GetPrefixedValue(dict, "grenaderGrenadeCooldownSeconds", 0));
		QueenGrenadeCooldownSeconds = Convert.ToSingle(GetPrefixedValue(dict, "queenGrenadeCooldownSeconds", 0));
		UnitFreezeDebuffTime = Convert.ToSingle(GetPrefixedValue(dict, "unitFreezeDebuffTime", 1));
		GrenaderGrenadeDamage = GetPrefixedIntValue(dict, "grenaderGrenadeDamage", 1);
		PsyPushSpeed = Convert.ToSingle(GetPrefixedValue(dict, "psyPushSpeed", 100));
		PsyMaxPushDistance = Convert.ToSingle(GetPrefixedValue(dict, "psyMaxPushDistance", 200));
		PsyPushForce = Convert.ToSingle(GetPrefixedValue(dict, "psyPushForce", 0.5f));
		DeadConversion = new OnDeadConversion().Load(dict);
		TimeToAutoHideWater = Convert.ToSingle(GetPrefixedValue(dict, "TimeToAutoHideWater", float.MaxValue));
		ImmortalTimeAfterResurrectSeconds = Convert.ToSingle(GetPrefixedValue(dict, "immortalTimeAfterResurrectSeconds", 0));
		RandomPushResist = Convert.ToSingle(GetPrefixedValue(dict, "randomPushResist", 0));
		AlienCopRestoreHP = GetPrefixedIntValue(dict, "alienCopRestoreHP", 0);
		BillManualShootTapRadius = GetPrefixedIntValue(dict, "billManualShootTapRadius", 1);
		InsectJumpDamage = GetPrefixedIntValue(dict, "insectJumpDamage", 0);
		InsectJumpColliderSize = VectorExtensions.Parse(GetPrefixedValue(dict, "insectJumpColliderSize", string.Empty).ToString());
		EggChargeCooldown = Convert.ToSingle(GetPrefixedValue(dict, "eggChargeCooldown", 0f), CultureInfo.InvariantCulture);
		TurretRechargeTime = Convert.ToSingle(GetPrefixedValue(dict, "turretRechargeTime", 0f), CultureInfo.InvariantCulture);
		TurretOnReloadDamage = GetPrefixedIntValue(dict, "turretOnReloadDamage", 0);
		GeneratorOnSpawnDamage = GetPrefixedIntValue(dict, "generatorOnSpawnDamage", 0);
		BuilderRunSelfDamage = GetPrefixedIntValue(dict, "builderRunSelfDamage", 0);
		BuilderRunSpecialSpeed = GetPrefixedIntValue(dict, "builderRunSpecialSpeed", 0);
		MedkitHealCount = GetPrefixedIntValue(dict, "medkitHealCount", 0);
		MedkitHealDelaySec = Convert.ToSingle(GetPrefixedValue(dict, "medkitHealDelaySec", 0f), CultureInfo.InvariantCulture);
		PVPReplicsCooldown = Convert.ToInt32(GetPrefixedValue(dict, "PVPReplicsCooldown", 15));
		CephalopodaSimpleAttackCooldown = Convert.ToInt32(GetPrefixedValue(dict, "cephalopodaSimpleAttackCooldown", 15));
		CephalopodaSimpleAttackDamage = Convert.ToInt32(GetPrefixedValue(dict, "cephalopodaSimpleAttackDamage", 150));
		CephalopodaFinalStrikeCooldown = Convert.ToInt32(GetPrefixedValue(dict, "cephalopodaFinalStrikeCooldown", 10));
		CephalopodaUltDamage = Convert.ToInt32(GetPrefixedValue(dict, "cephalopodaUltDamage", 999));
		CephalopodaFireRepeatDamage = Convert.ToInt32(GetPrefixedIntValue(dict, "cephalopodaFireRepeatDamage", 10));
		AustinBattleLoot = (string)GetPrefixedValue(dict, "austinBattleLoot", string.Empty);
		SwatFireChance = Convert.ToInt32(GetPrefixedValue(dict, "swatFireChance", 0));
		SwatFireDamage = Convert.ToInt32(GetPrefixedValue(dict, "swatFireDamage", 0));
		ChopperPercentForHeal = Convert.ToSingle(GetPrefixedValue(dict, "chopperPercentForHeal", 1f), CultureInfo.InvariantCulture);
		ChopperHealCooldown = Convert.ToSingle(GetPrefixedValue(dict, "chopperHealCooldown", 1f), CultureInfo.InvariantCulture);
		CarolJumpRangeSqrMagnitude = (float)Math.Pow(Convert.ToSingle(GetPrefixedValue(dict, "carolJumpMinDistance", 9999f), CultureInfo.InvariantCulture), 2.0);

		// 加载 CorpseType 映射数据
		LoadCorpseTypeMapping(dict);

		return true;
	}

	private void LoadCorpseTypeMapping(Dictionary<string, object> dict)
	{
		Debug.LogError("start LoadCorpseTypeMapping---------------");
		CorpseTypeMapping = new Dictionary<CreatureTemplate.CreatureType, CreatureTemplate.CreatureType>();
		// CorpseTypeMapping.Clear();

		// 从配置中获取 CorpseType 字典
		Dictionary<string, object> corpseTypeDict = CommonReader.GetValues(dict, "CorpseType", false);
		Debug.LogError("corpseTypeDict-----" + corpseTypeDict.Count);
		if (corpseTypeDict != null)
		{
			foreach (var kvp in corpseTypeDict)
			{
				string fromUnitStr = kvp.Key;
				string toUnitStr = kvp.Value.ToString();

				// 解析单位类型
				CreatureTemplate.CreatureType fromUnit;
				CreatureTemplate.CreatureType toUnit;

				if (CreatureTemplate.ParseUnit(fromUnitStr, out fromUnit, false) &&
					CreatureTemplate.ParseUnit(toUnitStr, out toUnit, false))
				{
					CorpseTypeMapping[fromUnit] = toUnit;
					Debug.Log($"加载尸体类型映射: {fromUnitStr} -> {toUnitStr}");
				}
				else
				{
					Debug.LogWarning($"无法解析尸体类型映射: {fromUnitStr} -> {toUnitStr}");
				}
			}
		}
		Debug.LogError("LoadCorpseTypeMapping---------------"+CorpseTypeMapping.Count);
	}

	/// <summary>
	/// 获取指定单位死亡后应该显示的尸体类型
	/// </summary>
	/// <param name="unitType">死亡的单位类型</param>
	/// <returns>尸体类型，如果没有映射则返回原单位类型</returns>
	public CreatureTemplate.CreatureType GetCorpseType(CreatureTemplate.CreatureType unitType)
	{
		if (CorpseTypeMapping.ContainsKey(unitType))
		{
			return CorpseTypeMapping[unitType];
		}
		return unitType; // 如果没有映射，返回原单位类型
	}
}
