using System;
using System.Collections;
using DG.Tweening;
using UnityEngine;

public class FxCreatureCorpse : MonoBehaviour
{
	private const float HidingLifeTime = 2f;

	[SerializeField]
	private SpriteRenderer spriteRenderer;

	[SerializeField]
	private Transform container3d;

	private bool destroyed;

	private Tweener tweenerHide;

	public Vector3 Scale
	{
		get
		{
			return spriteRenderer.transform.localScale;
		}
		set
		{
			spriteRenderer.transform.localScale = value;
		}
	}

	public Color SpriteBlendColor
	{
		get
		{
			return spriteRenderer.material.GetColor(Colors.LerpColor.Id);
		}
		set
		{
			spriteRenderer.material.SetColor(Colors.LerpColor.Id, value);
		}
	}

	public event Action<FxCreatureCorpse> CorpseLifeTimeEnded = delegate
	{
	};

	public void Init(CreatureTemplate.CreatureType corpseType, float corpseLifeTime, float flip)
	{
		// 使用配置中的尸体类型映射
		CreatureTemplate.CreatureType actualCorpseType = BattleParams.Instance.GetCorpseType(corpseType);

		CreatureTemplate.ApplyAnimOffsetY(actualCorpseType, spriteRenderer.transform);
		StartCoroutine(Coroutine(actualCorpseType, corpseLifeTime, flip));
	}

	private IEnumerator Coroutine(CreatureTemplate.CreatureType corpseType, float corpseLifeTime, float flip)
	{
		GameObject unit3d = createUnit3d(corpseType);
		if (unit3d == null)
		{
			spriteRenderer.sprite = GameController.Instance.AssetBundleLoaderUnitData.LoadBundle(corpseType).SpriteDeath;
			spriteRenderer.gameObject.SetActive(true);
		}
		else
		{
			if(container3d.childCount>0){
				container3d.DestroyChilds();
			}
			
			spriteRenderer.gameObject.SetActive(false);
			unit3d.transform.SetParent(container3d);
			unit3d.transform.localPosition = Vector3.zero;
			unit3d.transform.localRotation = Quaternion.identity;
			unit3d.transform.localScale = new Vector3(flip, 1f, 1f);
			Animator animator = unit3d.GetComponent<Animator>();
			animator.Play("Death", 0, 1f);
		}
		float started = Time.fixedTime;
		while (Time.fixedTime - started < corpseLifeTime)
		{
			yield return WaitFor.FixedUpdate;
		}
		if (tweenerHide == null && !destroyed)
		{
			this.CorpseLifeTimeEnded(this);
		}
	}

	private GameObject createUnit3d(CreatureTemplate.CreatureType corpseType)
	{
		string prefab3dpath = string.Format(@"AssetBundles/battleunits3d/{0}/{0}_prefab", corpseType);
		//Debug.LogError("add battle unit===========>" + prefab3dpath);
		GameObject _prefab = (GameObject)Resources.Load(prefab3dpath);
		if(_prefab!=null)
		{
			Transform _effects = _prefab.transform.Find("3d/Effects");
			if (_effects != null)
			{
				for(int i=0;i< _effects.childCount;i++)
				{
					_effects.GetChild(i).gameObject.SetActive(false);
				}
			}
		}
		return _prefab == null ? null : Instantiate(_prefab);
	}

	public void Hide(bool fade)
	{
		if (destroyed)
		{
			return;
		}
		if (fade)
		{
			if (tweenerHide == null)
			{
				tweenerHide = DOTween.ToAlpha(() => spriteRenderer.color, delegate (Color x)
				{
					spriteRenderer.color = x;
				}, 0f, 2f).OnComplete(OnHideFinished);
			}
		}
		else
		{
			DestroyNow();
		}
	}

	private void OnHideFinished()
	{
		tweenerHide = null;
		DestroyNow();
	}

	private void DestroyNow()
	{
		if (!destroyed)
		{
			destroyed = true;
			TweenUtils.KillAndNull(ref tweenerHide);
			UnityEngine.Object.Destroy(base.gameObject);
		}
	}
}
